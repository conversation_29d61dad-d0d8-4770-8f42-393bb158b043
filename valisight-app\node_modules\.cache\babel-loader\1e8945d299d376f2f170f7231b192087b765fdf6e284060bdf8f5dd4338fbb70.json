{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\TableOfContents.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TableOfContents = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  contentTextStyle = {},\n  subHeadingTextStyle = {},\n  reportData = null,\n  contentSettings = null\n}) => {\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Function to calculate dynamic TOC items based on enabled charts\n  const calculateDynamicTocItems = () => {\n    const tocItems = [];\n    let currentPageNumber = 1;\n\n    // 1. Report Summary - Always included (3 pages)\n    tocItems.push({\n      text: \"Report Summary\",\n      page: currentPageNumber\n    });\n    currentPageNumber += 3;\n\n    // 2. Fiscal Year Component\n    const fiscalYearCharts = ['incomeSummary', 'netIncome', 'grossProfitMargin', 'netProfitMargin'];\n    const enabledFiscalYearCharts = fiscalYearCharts.filter(chart => shouldDisplayChart(chart));\n\n    // Fiscal Year always shows (even if all charts disabled) - 1 or 2 pages based on enabled charts\n    let fiscalYearPages = 1;\n    if (enabledFiscalYearCharts.length >= 3) {\n      fiscalYearPages = 2;\n    }\n    tocItems.push({\n      text: \"Current Fiscal Year\",\n      page: currentPageNumber\n    });\n    currentPageNumber += fiscalYearPages;\n\n    // 3. Expense Summary Component\n    const expenseSummaryCharts = ['roaAndRoe', 'expensesTopAccounts', 'expensesTopAccountsMonthly', 'expensesWagesVsRevenueMonthly'];\n    const enabledExpenseSummaryCharts = expenseSummaryCharts.filter(chart => shouldDisplayChart(chart));\n\n    // Only include if at least one chart is enabled\n    if (enabledExpenseSummaryCharts.length > 0) {\n      let expenseSummaryPages = 1;\n      if (enabledExpenseSummaryCharts.length >= 3) {\n        expenseSummaryPages = 2;\n      }\n      tocItems.push({\n        text: \"Expense Summary\",\n        page: currentPageNumber\n      });\n      currentPageNumber += expenseSummaryPages;\n    }\n\n    // 4. Operational Efficiency Component\n    const operationalEfficiencyCharts = ['daysSalesOutstanding', 'daysPayablesOutstanding', 'daysInventoryOutstanding', 'cashConversionCycle', 'fixedAssetTurnover'];\n    const enabledOperationalEfficiencyCharts = operationalEfficiencyCharts.filter(chart => shouldDisplayChart(chart));\n\n    // Only include if at least one chart is enabled\n    if (enabledOperationalEfficiencyCharts.length > 0) {\n      let operationalEfficiencyPages = 1;\n      if (enabledOperationalEfficiencyCharts.length >= 4) {\n        operationalEfficiencyPages = 2;\n      }\n      tocItems.push({\n        text: \"Operational Efficiency\",\n        page: currentPageNumber\n      });\n      currentPageNumber += operationalEfficiencyPages;\n    }\n\n    // 5. Liquidity Summary Component\n    const liquiditySummaryCharts = ['netChangeInCash', 'quickRatio', 'monthsCashOnHand'];\n    const enabledLiquiditySummaryCharts = liquiditySummaryCharts.filter(chart => shouldDisplayChart(chart));\n\n    // Only include if at least one chart is enabled\n    if (enabledLiquiditySummaryCharts.length > 0) {\n      tocItems.push({\n        text: \"Liquidity Summary\",\n        page: currentPageNumber\n      });\n      currentPageNumber += 1;\n    }\n\n    // 6. Profit and Loss Reports\n    const plReports = ['thirteenMonthTrailing', 'monthly', 'ytd'];\n    const enabledPLReports = plReports.filter(report => shouldDisplayChart(report));\n\n    // Only include if at least one P&L report is enabled\n    if (enabledPLReports.length > 0) {\n      // Add individual P&L reports that are enabled\n      if (shouldDisplayChart('thirteenMonthTrailing')) {\n        tocItems.push({\n          text: \"Profit & Loss - 13 Month Trailing\",\n          page: currentPageNumber\n        });\n        currentPageNumber += 4;\n      }\n      if (shouldDisplayChart('monthly')) {\n        tocItems.push({\n          text: \"Profit & Loss - Monthly\",\n          page: currentPageNumber\n        });\n        currentPageNumber += 3;\n      }\n      if (shouldDisplayChart('ytd')) {\n        tocItems.push({\n          text: \"Profit & Loss - YTD\",\n          page: currentPageNumber\n        });\n        currentPageNumber += 2;\n      }\n    }\n\n    // 7. Balance Sheet\n    if (shouldDisplayChart('balanceSheet')) {\n      tocItems.push({\n        text: \"Balance Sheet\",\n        page: currentPageNumber\n      });\n      currentPageNumber += 3;\n    }\n    return tocItems;\n  };\n\n  // Get dynamic TOC items based on enabled charts\n  const tocItems = calculateDynamicTocItems();\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col n p-10  h-[297mm]\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYEndYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center flex justify-center   pb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl font-light text-gray-800 m-0\",\n            style: headingTextStyle,\n            children: \"Table of Contents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-2xl mx-auto w-full px-20 space-y-1\",\n          children: tocItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n                flex items-baseline\t py-2 px-2\n                ${index === tocItems.length - 1 ? '' : ''}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-gray-700 font-medium flex-shrink-0\",\n              style: contentTextStyle,\n              children: item.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-teal-600 font-semibold flex-shrink-0\",\n              style: contentTextStyle,\n              children: item.page\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_c = TableOfContents;\nexport default TableOfContents;\nvar _c;\n$RefreshReg$(_c, \"TableOfContents\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TableOfContents", "headerTextStyle", "headingTextStyle", "contentTextStyle", "subHeadingTextStyle", "reportData", "contentSettings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "calculateDynamicTocItems", "tocItems", "currentPageNumber", "push", "text", "page", "fiscalYearCharts", "enabledFiscalYearCharts", "filter", "chart", "fiscalYearPages", "length", "expense<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enabledExpenseSummaryCharts", "expenseSummaryPages", "operationalEfficiencyCharts", "enabledOperationalEfficiencyCharts", "operationalEfficiencyPages", "liquiditySummaryCharts", "enabledLiquiditySummaryCharts", "plReports", "enabledPLReports", "report", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "formatCompanyName", "companyName", "substring", "formatHeaderStyle", "style", "fontSize", "parseInt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYEndYear", "FYStartMonth", "map", "item", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/TableOfContents.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst TableOfContents = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  contentTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  reportData = null,\r\n  contentSettings = null\r\n}) => {\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Function to calculate dynamic TOC items based on enabled charts\r\n  const calculateDynamicTocItems = () => {\r\n    const tocItems = [];\r\n    let currentPageNumber = 1;\r\n\r\n    // 1. Report Summary - Always included (3 pages)\r\n    tocItems.push({\r\n      text: \"Report Summary\",\r\n      page: currentPageNumber\r\n    });\r\n    currentPageNumber += 3;\r\n\r\n    // 2. Fiscal Year Component\r\n    const fiscalYearCharts = ['incomeSummary', 'netIncome', 'grossProfitMargin', 'netProfitMargin'];\r\n    const enabledFiscalYearCharts = fiscalYearCharts.filter(chart => shouldDisplayChart(chart));\r\n\r\n    // Fiscal Year always shows (even if all charts disabled) - 1 or 2 pages based on enabled charts\r\n    let fiscalYearPages = 1;\r\n    if (enabledFiscalYearCharts.length >= 3) {\r\n      fiscalYearPages = 2;\r\n    }\r\n\r\n    tocItems.push({\r\n      text: \"Current Fiscal Year\",\r\n      page: currentPageNumber\r\n    });\r\n    currentPageNumber += fiscalYearPages;\r\n\r\n    // 3. Expense Summary Component\r\n    const expenseSummaryCharts = ['roaAndRoe', 'expensesTopAccounts', 'expensesTopAccountsMonthly', 'expensesWagesVsRevenueMonthly'];\r\n    const enabledExpenseSummaryCharts = expenseSummaryCharts.filter(chart => shouldDisplayChart(chart));\r\n\r\n    // Only include if at least one chart is enabled\r\n    if (enabledExpenseSummaryCharts.length > 0) {\r\n      let expenseSummaryPages = 1;\r\n      if (enabledExpenseSummaryCharts.length >= 3) {\r\n        expenseSummaryPages = 2;\r\n      }\r\n\r\n      tocItems.push({\r\n        text: \"Expense Summary\",\r\n        page: currentPageNumber\r\n      });\r\n      currentPageNumber += expenseSummaryPages;\r\n    }\r\n\r\n    // 4. Operational Efficiency Component\r\n    const operationalEfficiencyCharts = ['daysSalesOutstanding', 'daysPayablesOutstanding', 'daysInventoryOutstanding', 'cashConversionCycle', 'fixedAssetTurnover'];\r\n    const enabledOperationalEfficiencyCharts = operationalEfficiencyCharts.filter(chart => shouldDisplayChart(chart));\r\n\r\n    // Only include if at least one chart is enabled\r\n    if (enabledOperationalEfficiencyCharts.length > 0) {\r\n      let operationalEfficiencyPages = 1;\r\n      if (enabledOperationalEfficiencyCharts.length >= 4) {\r\n        operationalEfficiencyPages = 2;\r\n      }\r\n\r\n      tocItems.push({\r\n        text: \"Operational Efficiency\",\r\n        page: currentPageNumber\r\n      });\r\n      currentPageNumber += operationalEfficiencyPages;\r\n    }\r\n\r\n    // 5. Liquidity Summary Component\r\n    const liquiditySummaryCharts = ['netChangeInCash', 'quickRatio', 'monthsCashOnHand'];\r\n    const enabledLiquiditySummaryCharts = liquiditySummaryCharts.filter(chart => shouldDisplayChart(chart));\r\n\r\n    // Only include if at least one chart is enabled\r\n    if (enabledLiquiditySummaryCharts.length > 0) {\r\n      tocItems.push({\r\n        text: \"Liquidity Summary\",\r\n        page: currentPageNumber\r\n      });\r\n      currentPageNumber += 1;\r\n    }\r\n\r\n    // 6. Profit and Loss Reports\r\n    const plReports = ['thirteenMonthTrailing', 'monthly', 'ytd'];\r\n    const enabledPLReports = plReports.filter(report => shouldDisplayChart(report));\r\n\r\n    // Only include if at least one P&L report is enabled\r\n    if (enabledPLReports.length > 0) {\r\n      // Add individual P&L reports that are enabled\r\n      if (shouldDisplayChart('thirteenMonthTrailing')) {\r\n        tocItems.push({\r\n          text: \"Profit & Loss - 13 Month Trailing\",\r\n          page: currentPageNumber\r\n        });\r\n        currentPageNumber += 4;\r\n      }\r\n\r\n      if (shouldDisplayChart('monthly')) {\r\n        tocItems.push({\r\n          text: \"Profit & Loss - Monthly\",\r\n          page: currentPageNumber\r\n        });\r\n        currentPageNumber += 3;\r\n      }\r\n\r\n      if (shouldDisplayChart('ytd')) {\r\n        tocItems.push({\r\n          text: \"Profit & Loss - YTD\",\r\n          page: currentPageNumber\r\n        });\r\n        currentPageNumber += 2;\r\n      }\r\n    }\r\n\r\n    // 7. Balance Sheet\r\n    if (shouldDisplayChart('balanceSheet')) {\r\n      tocItems.push({\r\n        text: \"Balance Sheet\",\r\n        page: currentPageNumber\r\n      });\r\n      currentPageNumber += 3;\r\n    }\r\n\r\n    return tocItems;\r\n  };\r\n\r\n  // Get dynamic TOC items based on enabled charts\r\n  const tocItems = calculateDynamicTocItems();\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n    \r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n    \r\n    return companyName;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col n p-10  h-[297mm]\">\r\n\r\n         {/* Header Section */}\r\n    <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}\r\n          </p>\r\n        </div>\r\n        \r\n        <div className='flex flex-col justify-center h-full'>\r\n\r\n        {/* Header Section */}\r\n        <div className=\"text-center flex justify-center   pb-6\">\r\n          <h1 \r\n            className=\"text-5xl font-light text-gray-800 m-0\"\r\n            style={headingTextStyle}\r\n          >\r\n            Table of Contents\r\n          </h1>\r\n        </div>\r\n\r\n       {/* Table of Contents Items */}\r\n        <div className=\"max-w-2xl mx-auto w-full px-20 space-y-1\">\r\n          {tocItems.map((item, index) => (\r\n            <div \r\n              key={index}\r\n              className={`\r\n                flex items-baseline\t py-2 px-2\r\n                ${index === tocItems.length - 1 ? '' : ''}\r\n              `}\r\n            >\r\n              <span \r\n                className=\"text-lg text-gray-700 font-medium flex-shrink-0\"\r\n                style={contentTextStyle}\r\n              >\r\n                {item.text}\r\n              </span>\r\n              <div className=\"flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4\"></div>\r\n              <span \r\n                className=\"text-lg text-teal-600 font-semibold flex-shrink-0\"\r\n                style={contentTextStyle}\r\n              >\r\n                {item.page}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TableOfContents;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAC;EACvBC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACF,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEG,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOH,eAAe,CAACG,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,QAAQ,GAAG,EAAE;IACnB,IAAIC,iBAAiB,GAAG,CAAC;;IAEzB;IACAD,QAAQ,CAACE,IAAI,CAAC;MACZC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAEH;IACR,CAAC,CAAC;IACFA,iBAAiB,IAAI,CAAC;;IAEtB;IACA,MAAMI,gBAAgB,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;IAC/F,MAAMC,uBAAuB,GAAGD,gBAAgB,CAACE,MAAM,CAACC,KAAK,IAAIZ,kBAAkB,CAACY,KAAK,CAAC,CAAC;;IAE3F;IACA,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIH,uBAAuB,CAACI,MAAM,IAAI,CAAC,EAAE;MACvCD,eAAe,GAAG,CAAC;IACrB;IAEAT,QAAQ,CAACE,IAAI,CAAC;MACZC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEH;IACR,CAAC,CAAC;IACFA,iBAAiB,IAAIQ,eAAe;;IAEpC;IACA,MAAME,oBAAoB,GAAG,CAAC,WAAW,EAAE,qBAAqB,EAAE,4BAA4B,EAAE,+BAA+B,CAAC;IAChI,MAAMC,2BAA2B,GAAGD,oBAAoB,CAACJ,MAAM,CAACC,KAAK,IAAIZ,kBAAkB,CAACY,KAAK,CAAC,CAAC;;IAEnG;IACA,IAAII,2BAA2B,CAACF,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAIG,mBAAmB,GAAG,CAAC;MAC3B,IAAID,2BAA2B,CAACF,MAAM,IAAI,CAAC,EAAE;QAC3CG,mBAAmB,GAAG,CAAC;MACzB;MAEAb,QAAQ,CAACE,IAAI,CAAC;QACZC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAEH;MACR,CAAC,CAAC;MACFA,iBAAiB,IAAIY,mBAAmB;IAC1C;;IAEA;IACA,MAAMC,2BAA2B,GAAG,CAAC,sBAAsB,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,oBAAoB,CAAC;IAChK,MAAMC,kCAAkC,GAAGD,2BAA2B,CAACP,MAAM,CAACC,KAAK,IAAIZ,kBAAkB,CAACY,KAAK,CAAC,CAAC;;IAEjH;IACA,IAAIO,kCAAkC,CAACL,MAAM,GAAG,CAAC,EAAE;MACjD,IAAIM,0BAA0B,GAAG,CAAC;MAClC,IAAID,kCAAkC,CAACL,MAAM,IAAI,CAAC,EAAE;QAClDM,0BAA0B,GAAG,CAAC;MAChC;MAEAhB,QAAQ,CAACE,IAAI,CAAC;QACZC,IAAI,EAAE,wBAAwB;QAC9BC,IAAI,EAAEH;MACR,CAAC,CAAC;MACFA,iBAAiB,IAAIe,0BAA0B;IACjD;;IAEA;IACA,MAAMC,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,kBAAkB,CAAC;IACpF,MAAMC,6BAA6B,GAAGD,sBAAsB,CAACV,MAAM,CAACC,KAAK,IAAIZ,kBAAkB,CAACY,KAAK,CAAC,CAAC;;IAEvG;IACA,IAAIU,6BAA6B,CAACR,MAAM,GAAG,CAAC,EAAE;MAC5CV,QAAQ,CAACE,IAAI,CAAC;QACZC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAEH;MACR,CAAC,CAAC;MACFA,iBAAiB,IAAI,CAAC;IACxB;;IAEA;IACA,MAAMkB,SAAS,GAAG,CAAC,uBAAuB,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7D,MAAMC,gBAAgB,GAAGD,SAAS,CAACZ,MAAM,CAACc,MAAM,IAAIzB,kBAAkB,CAACyB,MAAM,CAAC,CAAC;;IAE/E;IACA,IAAID,gBAAgB,CAACV,MAAM,GAAG,CAAC,EAAE;MAC/B;MACA,IAAId,kBAAkB,CAAC,uBAAuB,CAAC,EAAE;QAC/CI,QAAQ,CAACE,IAAI,CAAC;UACZC,IAAI,EAAE,mCAAmC;UACzCC,IAAI,EAAEH;QACR,CAAC,CAAC;QACFA,iBAAiB,IAAI,CAAC;MACxB;MAEA,IAAIL,kBAAkB,CAAC,SAAS,CAAC,EAAE;QACjCI,QAAQ,CAACE,IAAI,CAAC;UACZC,IAAI,EAAE,yBAAyB;UAC/BC,IAAI,EAAEH;QACR,CAAC,CAAC;QACFA,iBAAiB,IAAI,CAAC;MACxB;MAEA,IAAIL,kBAAkB,CAAC,KAAK,CAAC,EAAE;QAC7BI,QAAQ,CAACE,IAAI,CAAC;UACZC,IAAI,EAAE,qBAAqB;UAC3BC,IAAI,EAAEH;QACR,CAAC,CAAC;QACFA,iBAAiB,IAAI,CAAC;MACxB;IACF;;IAEA;IACA,IAAIL,kBAAkB,CAAC,cAAc,CAAC,EAAE;MACtCI,QAAQ,CAACE,IAAI,CAAC;QACZC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAEH;MACR,CAAC,CAAC;MACFA,iBAAiB,IAAI,CAAC;IACxB;IAEA,OAAOD,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMA,QAAQ,GAAGD,wBAAwB,CAAC,CAAC;EAE3C,MAAMuB,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAED,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAAClB,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOkB,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG;MAAE,GAAGzC;IAAgB,CAAC;IACpC,IAAIyC,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAGC,QAAQ,CAACF,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAGD,oBACE3C,OAAA;IAAK8C,SAAS,EAAC,KAAK;IAAAC,QAAA,eAGlB/C,OAAA;MAAK8C,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBAGvF/C,OAAA;QAAK8C,SAAS,EAAC,+FAA+F;QAAAC,QAAA,gBACxG/C,OAAA;UACE8C,SAAS,EAAC,sCAAsC;UAChDH,KAAK,EAAEzC;QAAgB;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErB,CAAC,eACLnD,OAAA;UAAG8C,SAAS,EAAC,2BAA2B;UAACH,KAAK,EAAED,iBAAiB,CAAC,CAAE;UAAAK,QAAA,GACjEb,kBAAkB,CAAC5B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8C,SAAS,EAAE9C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+C,YAAY,CAAC,EAAC,KAAG,EAACd,iBAAiB,CAACjC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,WAAW,CAAC;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAGpD/C,OAAA;UAAK8C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD/C,OAAA;YACE8C,SAAS,EAAC,uCAAuC;YACjDH,KAAK,EAAExC,gBAAiB;YAAA4C,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACtDnC,QAAQ,CAAC0C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBxD,OAAA;YAEE8C,SAAS,EAAE;AACzB;AACA,kBAAkBU,KAAK,KAAK5C,QAAQ,CAACU,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AACzD,eAAgB;YAAAyB,QAAA,gBAEF/C,OAAA;cACE8C,SAAS,EAAC,iDAAiD;cAC3DH,KAAK,EAAEvC,gBAAiB;cAAA2C,QAAA,EAEvBQ,IAAI,CAACxC;YAAI;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACPnD,OAAA;cAAK8C,SAAS,EAAC;YAAiE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvFnD,OAAA;cACE8C,SAAS,EAAC,mDAAmD;cAC7DH,KAAK,EAAEvC,gBAAiB;cAAA2C,QAAA,EAEvBQ,IAAI,CAACvC;YAAI;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAlBFK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GA1OIxD,eAAe;AA4OrB,eAAeA,eAAe;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}