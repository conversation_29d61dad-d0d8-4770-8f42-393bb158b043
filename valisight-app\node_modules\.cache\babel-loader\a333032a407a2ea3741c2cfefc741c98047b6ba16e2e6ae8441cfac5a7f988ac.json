{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\CoverPage.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// import coverImage from './../../../assets/cover.png';\n// import logo from './../../../assets/LogoNew.png';\n// import qboButton from \"./../../../assets/C2QB_green_btn_med_default.svg\";\n// import coverImagee from \"./../../../assets/logos/Top-Elements-v2.svg\";\n\nconst DeepSightCoverPage = ({\n  reportData = null\n}) => {\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \";\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col h-[400mm]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center h-full relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://valisight-dev.s3.us-east-1.amazonaws.com/top_image.png\",\n            alt: \"Cover Page\",\n            className: \"w-full h-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pl-24 mb-24\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-light text-gray-600 mb-2 font-medium font-open-sans\",\n              children: \"Prepared For:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-8xl font-bold text-gray-800 mb-2 font-open-sans\",\n              title: reportData === null || reportData === void 0 ? void 0 : reportData.companyName // Show full name on hover\n              ,\n              children: formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-6xl w-1/2 pb-6 font-semibold text-[#1E7C8C] border-b border-gray-800 font-open-sans\",\n              children: formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYEndYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end pr-24  mt-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" mt-auto flex flex-col justify-end h-1/4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl text-right mb-2 font-open-sans\",\n                children: \"Prepared By:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://valisight-dev.s3.us-east-1.amazonaws.com/logo.png\",\n                alt: \"Logo\",\n                width: 200\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-6xl text-gray-800 font-bold font-open-sans\",\n                style: {\n                  marginLeft: '-50px'\n                },\n                children: \"VALISIGHTS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_c = DeepSightCoverPage;\nexport default DeepSightCoverPage;\nvar _c;\n$RefreshReg$(_c, \"DeepSightCoverPage\");", "map": {"version": 3, "names": ["DeepSightCoverPage", "reportData", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "formatCompanyName", "companyName", "length", "substring", "_jsxDEV", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "FYEndYear", "FYStartMonth", "width", "style", "marginLeft", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/CoverPage.jsx"], "sourcesContent": ["// import coverImage from './../../../assets/cover.png';\r\n// import logo from './../../../assets/LogoNew.png';\r\n// import qboButton from \"./../../../assets/C2QB_green_btn_med_default.svg\";\r\n// import coverImagee from \"./../../../assets/logos/Top-Elements-v2.svg\";\r\n\r\nconst DeepSightCoverPage = ({\r\n  reportData = null\r\n}) => {\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; \r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n    \r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n    \r\n    return companyName;\r\n  };\r\n\r\n  return (\r\n    <div className=\" p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col h-[400mm]\">\r\n\r\n\r\n\r\n        {/* Main Content Area */}\r\n        <div className='flex flex-col justify-center h-full relative'>\r\n\r\n          {/* Top Section with Current Financial State Analysis */}\r\n          <div className=\"relative mb-12\">\r\n            <img src=\"https://valisight-dev.s3.us-east-1.amazonaws.com/top_image.png\" alt=\"Cover Page\" className=\"w-full h-auto\" />\r\n          </div>\r\n\r\n          {/* Middle Content Section */}\r\n          <div className=\"pl-24 mb-24\">\r\n            <div className=\"mb-8\">\r\n              <p\r\n                className=\"text-3xl font-light text-gray-600 mb-2 font-medium font-open-sans\"\r\n              >\r\n                Prepared For:\r\n              </p>\r\n              <h2\r\n                className=\"text-8xl font-bold text-gray-800 mb-2 font-open-sans\"\r\n                title={reportData?.companyName} // Show full name on hover\r\n              >\r\n                {formatCompanyName(reportData?.companyName)}\r\n              </h2>\r\n            </div>\r\n\r\n            <div>\r\n              <p\r\n                className=\"text-6xl w-1/2 pb-6 font-semibold text-[#1E7C8C] border-b border-gray-800 font-open-sans\"\r\n              >\r\n               {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Bottom Section with Logo */}\r\n          <div className='flex justify-end pr-24  mt-20'>\r\n            <div className=\" mt-auto flex flex-col justify-end h-1/4\">\r\n              <div className=\"\">\r\n                <p\r\n                  className=\"text-3xl text-right mb-2 font-open-sans\"\r\n                >\r\n                  Prepared By:\r\n                </p>\r\n              </div>\r\n              <div className='flex items-center text-right'>\r\n                <img src=\"https://valisight-dev.s3.us-east-1.amazonaws.com/logo.png\" alt=\"Logo\" width={200} />\r\n                <p className='text-6xl text-gray-800 font-bold font-open-sans' style={{ marginLeft: '-50px' }}>VALISIGHTS</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DeepSightCoverPage;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,MAAMA,kBAAkB,GAAGA,CAAC;EAC1BC,UAAU,GAAG;AACf,CAAC,KAAK;EAEJ,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG;IACZ;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAED,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAACC,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOF,WAAW;EACpB,CAAC;EAED,oBACEG,OAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,QAAA,eAEnBF,OAAA;MAAKC,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eAKjEF,OAAA;QAAKC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAG3DF,OAAA;UAAKC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BF,OAAA;YAAKG,GAAG,EAAC,gEAAgE;YAACC,GAAG,EAAC,YAAY;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC,eAGNR,OAAA;UAAKC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BF,OAAA;YAAKC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBF,OAAA;cACEC,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC9E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cACEC,SAAS,EAAC,sDAAsD;cAChEQ,KAAK,EAAEnB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAY,CAAC;cAAA;cAAAK,QAAA,EAE/BN,iBAAiB,CAACN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENR,OAAA;YAAAE,QAAA,eACEF,OAAA;cACEC,SAAS,EAAC,0FAA0F;cAAAC,QAAA,EAEpGX,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,SAAS,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,YAAY;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNR,OAAA;UAAKC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAC5CF,OAAA;YAAKC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDF,OAAA;cAAKC,SAAS,EAAC,EAAE;cAAAC,QAAA,eACfF,OAAA;gBACEC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACpD;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNR,OAAA;cAAKC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CF,OAAA;gBAAKG,GAAG,EAAC,2DAA2D;gBAACC,GAAG,EAAC,MAAM;gBAACQ,KAAK,EAAE;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9FR,OAAA;gBAAGC,SAAS,EAAC,iDAAiD;gBAACY,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAQ,CAAE;gBAAAZ,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA1FI1B,kBAAkB;AA4FxB,eAAeA,kBAAkB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}