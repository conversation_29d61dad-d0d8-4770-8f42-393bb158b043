{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\ReportSummary.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportSummary = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  h2TextStyle = {},\n  h3TextStyle = {},\n  reportData = null\n}) => {\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYEndYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-4\",\n          style: headingTextStyle,\n          children: \"Executive Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700 \",\n          style: contentTextStyle,\n          children: [reportData === null || reportData === void 0 ? void 0 : reportData.companyName, \" has demonstrated significant financial improvement as of January 2025, reporting a notable increase in net profit compared to the previous year. The company achieved a YTD Net Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3 million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up from a loss in the previous year. This positive trend is driven by increased sales, improved cost management, and operational efficiency.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl font-semibold text-teal-600\",\n        style: headingTextStyle,\n        children: \"Analysis and Actionable Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Profitability Ratios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Profit Margin has increased 33% from a negative margin the previous year. This indicates improved profitability and cost management.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Gross Profit Margin has risen to 56%, highlighting better control over production costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: h3TextStyle,\n          children: [\"Recommendation\", /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              marginTop: '-4px'\n            },\n            children: \"Maintain the current cost management strategies, particularly focusing on sustaining high-margin product lines and reducing labor inefficiencies.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Revenue and Income Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: h3TextStyle,\n              children: \"Significant Sales Growth\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), \" Total income increased from $3.4 million in Jan 2024 to $5.2 million in Jan 2025.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: h3TextStyle,\n            children: \"Key Product Performance:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 05 - Top performer with sales of $1.63 million.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight Sales - Substantial growth to $1.31 million, up from $485K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Packaging Sales - Increased to $633K from $295K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: h3TextStyle,\n            children: \"Underperforming Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 02- Decreased sales from $72K to $6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 04- Signifcant drop from $158K to $1K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Focus on promoting high-performing products (like Product 05) while reassessing the strategy for underperforming items, particularly Products 02 and 04.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYEndYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Cost Management and COGS Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"COGS as a percentage of income reduced from 65% to 44%.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: h3TextStyle,\n            children: \"Cost Reductions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight COGS - Increased by 21%, indicating higher volume of sales.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Labor COGS - Improved management with costs decreasing in several product lines.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Material Costs - Significant reductions in Product 04 and Product 05.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue focusing on labor efficiency and explore more strategic sourcing for raw materials to reduce material cost variations.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Expense Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: h3TextStyle,\n            children: \"Major Reductions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation Expense- Reduced by $161K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Sales Commissions- Down by $136K, aligning commission structures with performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: h3TextStyle,\n            children: \"Areas of Increase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Rent Expense- Increased by 49.6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Up by $87K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and exploring remote work options to mitigate rent costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Equity (ROE) improved to 12%, reflecting better management of equity resources.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold\",\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Maintain or enhance operational efficiency by investing in technology that maximizes asset utilization and minimizes downtime.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Liquidity and Cash Flow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Change in Cash was positive $1.4 million in January 2025.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold\",\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue maintaining liquidity by preserving cash reserves and managing receivables more efciently.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-4 p-10 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYEndYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: h2TextStyle,\n          children: \"Expense Breakdown and Recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: h3TextStyle,\n            children: \"High-Impact Expenses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation- Despite reductions, still the largest single expense.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Continues to be a signifcant cost driver.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: h3TextStyle,\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Consider asset revaluation to reduce depreciation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: contentTextStyle,\n            children: \"Optimize G&A expenses by streamlining administrative processes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white pb-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-3\",\n          style: headingTextStyle,\n          children: \"Key Focus Areas for 2025\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700\",\n          style: contentTextStyle,\n          children: [reportData === null || reportData === void 0 ? void 0 : reportData.companyName, \" has demonstrated significant financial improvement, particularly in profitability and operational efficiency. Strategic cost management and increased sales have positively impacted the bottom line. Moving forward, focusing on optimizing underperforming products, managing rent and salary costs, and maintaining liquidity will ensure continued financial stability and growth.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs py-9 px-12 mt-auto\",\n        style: {\n          position: 'absolute',\n          left: 0,\n          right: 0,\n          bottom: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c = ReportSummary;\nexport default ReportSummary;\nvar _c;\n$RefreshReg$(_c, \"ReportSummary\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ReportSummary", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "h2TextStyle", "h3TextStyle", "reportData", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "formatCompanyName", "companyName", "length", "substring", "formatHeaderStyle", "style", "fontSize", "parseInt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYEndYear", "FYStartMonth", "marginTop", "position", "left", "right", "bottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/ReportSummary.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ReportSummary = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  h2TextStyle = {},\r\n  h3TextStyle = {},\r\n  reportData = null\r\n}) => {\r\n\r\n    const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n    const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n    \r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n    \r\n    return companyName;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n    \r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  return (\r\n    <div className=\" p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\">\r\n\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Executive Summary Section */}\r\n        <div className=\"bg-white mb-2\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-4\" style={headingTextStyle}>\r\n            Executive Summary\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700 \" style={contentTextStyle}>\r\n            {reportData?.companyName} has demonstrated significant financial improvement as of January 2025, reporting a\r\n            notable increase in net profit compared to the previous year. The company achieved a YTD Net\r\n            Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3\r\n            million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up\r\n            from a loss in the previous year. This positive trend is driven by increased sales, improved cost\r\n            management, and operational efficiency.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Analysis and Recommendations Header */}\r\n        <div className=\"text-2xl font-semibold text-teal-600\" style={headingTextStyle}>\r\n          Analysis and Actionable Recommendations\r\n        </div>\r\n\r\n        {/* Profitability Ratios */}\r\n        <div>\r\n          <h3 style={h2TextStyle}>\r\n            Profitability Ratios\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Profit Margin has increased 33% from a negative margin the previous year. This\r\n              indicates improved profitability and cost management.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Gross Profit Margin has risen to 56%, highlighting better control over production\r\n              costs.\r\n            </p>\r\n          </div>\r\n          <div\r\n           style={h3TextStyle}>\r\n            Recommendation\r\n            <div style={{ ...contentTextStyle, marginTop: '-4px' }}>\r\n              Maintain the current cost management strategies, particularly focusing on sustaining\r\n              high-margin product lines and reducing labor inefficiencies.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Revenue and Income Analysis */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={h2TextStyle}>\r\n            Revenue and Income Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              <span style={h3TextStyle}>Significant Sales Growth</span> Total income increased from $3.4 million\r\n              in Jan 2024 to $5.2 million in Jan 2025.\r\n            </p>\r\n            <p style={h3TextStyle}>\r\n              Key Product Performance:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 05 - Top performer with sales of $1.63 million.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight Sales - Substantial growth to $1.31 million, up from $485K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Packaging Sales - Increased to $633K from $295K.\r\n              </p>\r\n            </div>\r\n            <p style={h3TextStyle}>\r\n              Underperforming Products\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 02- Decreased sales from $72K to $6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 04- Signifcant drop from $158K to $1K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Focus on promoting high-performing products (like Product 05) while reassessing the\r\n              strategy for underperforming items, particularly Products 02 and 04.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cost Management and COGS Analysis */}\r\n        <div>\r\n          <h3 style={h2TextStyle}>\r\n            Cost Management and COGS Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              COGS as a percentage of income reduced from 65% to 44%.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={h3TextStyle}>\r\n              Cost Reductions:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight COGS - Increased by 21%, indicating higher volume of sales.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Labor COGS - Improved management with costs decreasing in several product lines.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Material Costs - Significant reductions in Product 04 and Product 05.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n             style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue focusing on labor efficiency and explore more strategic sourcing for raw\r\n              materials to reduce material cost variations.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Expense Management */}\r\n        <div>\r\n          <h3 style={h2TextStyle}>\r\n            Expense Management\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={h3TextStyle}>\r\n              Major Reductions\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation Expense- Reduced by $161K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Sales Commissions- Down by $136K, aligning commission structures with performance.\r\n              </p>\r\n            </div>\r\n            <p className=\"leading-relaxed font-semibold\" style={h3TextStyle}>\r\n              Areas of Increase\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Rent Expense- Increased by 49.6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Up by $87K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n             style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and\r\n              exploring remote work options to mitigate rent costs.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Operational Efficiency */}\r\n        <div>\r\n          <h3 style={h2TextStyle}>\r\n            Operational Efficiency\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Equity (ROE) improved to 12%, reflecting better management of equity\r\n              resources.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold\"\r\n             style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Maintain or enhance operational efficiency by investing in technology that maximizes\r\n              asset utilization and minimizes downtime.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Liquidity and Cash Flow */}\r\n        <div className='mb-2'>\r\n          <h3 style={h2TextStyle}>\r\n            Liquidity and Cash Flow\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Change in Cash was positive $1.4 million in January 2025.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold\"\r\n             style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue maintaining liquidity by preserving cash reserves and managing receivables more\r\n              efciently.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-4 p-10 relative\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)} \r\n          </p>\r\n        </div>\r\n\r\n        {/* Expense Breakdown and Recommendations*/}\r\n        <div className=\"mb-2\">\r\n          <h3 style={h2TextStyle}>\r\n            Expense Breakdown and Recommendations\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed font-semibold\" style={h3TextStyle}>\r\n              High-Impact Expenses\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation- Despite reductions, still the largest single expense.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Continues to be a signifcant cost driver.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n             style={h3TextStyle}>\r\n              Recommendation\r\n            </div>\r\n            <p className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Consider asset revaluation to reduce depreciation.\r\n            </p>\r\n            <p style={contentTextStyle}>\r\n              Optimize G&A expenses by streamlining administrative processes.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Key Focus Areas for 2025 */}\r\n        <div className=\"bg-white pb-5\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-3\" style={headingTextStyle}>\r\n            Key Focus Areas for 2025\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700\" style={contentTextStyle}>\r\n            {reportData?.companyName} has demonstrated significant financial improvement, particularly in profitability and\r\n            operational efficiency. Strategic cost management and increased sales have positively impacted\r\n            the bottom line. Moving forward, focusing on optimizing underperforming products, managing\r\n            rent and salary costs, and maintaining liquidity will ensure continued financial stability and\r\n            growth.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer - Always stick to bottom */}\r\n        <div\r\n          className='text-center text-slate-300 text-xs py-9 px-12 mt-auto'\r\n          style={{ position: 'absolute', left: 0, right: 0, bottom: 0 }}\r\n        >\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportSummary;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EACrBC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,WAAW,GAAG,CAAC,CAAC;EAChBC,WAAW,GAAG,CAAC,CAAC;EAChBC,UAAU,GAAG;AACf,CAAC,KAAK;EAEF,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACtD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAEC,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IAC3C,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAACC,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOF,WAAW;EACpB,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG;MAAE,GAAGjB;IAAgB,CAAC;IAEpC,IAAIiB,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAGC,QAAQ,CAACF,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,oBACEnB,OAAA;IAAKsB,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnBvB,OAAA;MAAKsB,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAGjFvB,OAAA;QAAKsB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGvB,OAAA;UAAImB,KAAK,EAAEjB,eAAgB;UAAAqB,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C3B,OAAA;UAAGsB,SAAS,EAAC,2BAA2B;UAACH,KAAK,EAAED,iBAAiB,CAAC,CAAE;UAAAK,QAAA,GACjEd,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,SAAS,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,YAAY,CAAC,EAAC,KAAG,EAACf,iBAAiB,CAACN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BvB,OAAA;UAAKsB,SAAS,EAAC,2CAA2C;UAACH,KAAK,EAAEhB,gBAAiB;UAAAoB,QAAA,EAAC;QAEpF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3B,OAAA;UAAGsB,SAAS,EAAC,wCAAwC;UAACH,KAAK,EAAEd,gBAAiB;UAAAkB,QAAA,GAC3Ef,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW,EAAC,kgBAM3B;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,sCAAsC;QAACH,KAAK,EAAEhB,gBAAiB;QAAAoB,QAAA,EAAC;MAE/E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGN3B,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN3B,OAAA;UACCmB,KAAK,EAAEZ,WAAY;UAAAgB,QAAA,GAAC,gBAEnB,eAAAvB,OAAA;YAAKmB,KAAK,EAAE;cAAE,GAAGd,gBAAgB;cAAEyB,SAAS,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,gBACrDvB,OAAA;cAAMmB,KAAK,EAAEZ,WAAY;cAAAgB,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,sFAE3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEvB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3B,OAAA;YAAGmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEvB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACEmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEtB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBACjFvB,OAAA;QAAKsB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGvB,OAAA;UAAImB,KAAK,EAAEjB,eAAgB;UAAAqB,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C3B,OAAA;UAAGsB,SAAS,EAAC,2BAA2B;UAACH,KAAK,EAAED,iBAAiB,CAAC,CAAE;UAAAK,QAAA,GACjEd,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,SAAS,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,YAAY,CAAC,EAAC,KAAG,EAACf,iBAAiB,CAACN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACCmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAErB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3B,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACCmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAErB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACEsB,SAAS,EAAC,eAAe;YAC1BH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAErB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGsB,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACEsB,SAAS,EAAC,eAAe;YAC1BH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAErB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAGxE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,wEAAwE;MAAAC,QAAA,gBACrFvB,OAAA;QAAKsB,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGvB,OAAA;UAAImB,KAAK,EAAEjB,eAAgB;UAAAqB,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C3B,OAAA;UAAGsB,SAAS,EAAC,2BAA2B;UAACH,KAAK,EAAED,iBAAiB,CAAC,CAAE;UAAAK,QAAA,GACjEd,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,SAAS,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,YAAY,CAAC,EAAC,KAAG,EAACf,iBAAiB,CAACN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBvB,OAAA;UAAImB,KAAK,EAAEb,WAAY;UAAAiB,QAAA,EAAC;QAExB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAGsB,SAAS,EAAC,iBAAiB;cAACH,KAAK,EAAEd,gBAAiB;cAAAkB,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACCmB,KAAK,EAAEZ,WAAY;YAAAgB,QAAA,EAAC;UAErB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3B,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAACH,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3B,OAAA;YAAGmB,KAAK,EAAEd,gBAAiB;YAAAkB,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BvB,OAAA;UAAKsB,SAAS,EAAC,2CAA2C;UAACH,KAAK,EAAEhB,gBAAiB;UAAAoB,QAAA,EAAC;QAEpF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3B,OAAA;UAAGsB,SAAS,EAAC,uCAAuC;UAACH,KAAK,EAAEd,gBAAiB;UAAAkB,QAAA,GAC1Ef,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,WAAW,EAAC,yXAK3B;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QACEsB,SAAS,EAAC,uDAAuD;QACjEH,KAAK,EAAE;UAAEY,QAAQ,EAAE,UAAU;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAX,QAAA,eAE9DvB,OAAA;UAAAuB,QAAA,EAAG;QAKH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GAjXIlC,aAAa;AAmXnB,eAAeA,aAAa;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}