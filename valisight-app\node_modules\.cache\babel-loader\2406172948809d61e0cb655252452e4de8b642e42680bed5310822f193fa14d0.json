{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\MonthTrailing.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfitLoss13MonthDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  subHeaderTextStyle = {},\n  contentTextStyle = {},\n  contentSettings = null,\n  reportData = null // Add reportData prop to receive API data\n}) => {\n  _s();\n  // Extract background color from headerTextStyle for table header\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\n\n  // Function to format currency values with $ prefix and comma separators\n  // Function to format currency values with $ prefix and comma separators\n  const formatCurrency = value => {\n    if (!value || value === '0' || value === 0) return '$0';\n\n    // Convert to number if it's a string\n    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[,$]/g, '')) : value;\n\n    // Check if the value is a valid number\n    if (isNaN(numValue)) return '$0';\n\n    // Handle negative values correctly\n    if (numValue < 0) {\n      return '-$' + Math.abs(numValue).toLocaleString('en-US', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    }\n\n    // Format positive values with commas and $ prefix\n    return '$' + numValue.toLocaleString('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    });\n  };\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Function to transform API data into the required format\n  const transformApiData = apiData => {\n    if (!apiData || !apiData.profitAndLossMonthsTrailing) {\n      return {\n        months: [],\n        tableData: []\n      };\n    }\n    const rawData = apiData.profitAndLossMonthsTrailing;\n\n    // Extract months from the first data item (excluding account_type and account_name)\n    const months = rawData.length > 0 ? Object.keys(rawData[0]).filter(key => key !== 'account_type' && key !== 'account_name') : [];\n\n    // Group data by account_type\n    const groupedData = rawData.reduce((acc, item) => {\n      const accountType = item.account_type;\n      if (!acc[accountType]) {\n        acc[accountType] = [];\n      }\n      acc[accountType].push(item);\n      return acc;\n    }, {});\n\n    // Transform grouped data into table format\n    const tableData = [];\n    Object.keys(groupedData).forEach(accountType => {\n      // Add header row for account type\n      tableData.push({\n        isHeader: true,\n        category: accountType\n      });\n\n      // Add data rows for this account type\n      groupedData[accountType].forEach(item => {\n        // Format the data with currency formatting\n        const rowData = months.map(month => formatCurrency(item[month] || '0'));\n\n        // Check if this is a total row (contains \"Total\" in account_name)\n        const isTotal = item.account_name.toLowerCase().includes('total');\n        tableData.push({\n          label: item.account_name,\n          data: rowData,\n          isTotal: isTotal,\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total')\n        });\n      });\n    });\n    return {\n      months,\n      tableData\n    };\n  };\n  useEffect(() => {\n    if (shouldDisplayChart('thirteenMonthTrailing')) {\n      transformApiData(reportData);\n    }\n  }, [reportData, contentSettings]);\n\n  // Transform the API data\n  const {\n    months,\n    tableData\n  } = transformApiData(reportData);\n\n  // Fallback to empty state if no data\n  if (!reportData || !reportData.profitAndLossMonthsTrailing || months.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-5 section profit-loss-section month-trailing\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter'\n            },\n            children: \"Profit and Loss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"13 Month Trailing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontSize: \"20px\",\n              color: \"black\",\n              fontWeight: 'lighter'\n            },\n            children: reportData === null || reportData === void 0 ? void 0 : reportData.companyName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"No data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 mt-2\",\n              children: \"Please check your data source\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  const renderTableRow = (item, index) => {\n    if (item.isHeader) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"font-bold text-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), months.map((_, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n          style: {\n            backgroundColor: i === months.length - 1 ? '#d2e9ea' : 'transparent'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this))]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), item.data.map((value, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px',\n            backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this))]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), item.data.map((value, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px',\n          backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\n        },\n        children: value\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this))]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this);\n  };\n  return shouldDisplayChart('thirteenMonthTrailing') ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5 section profit-loss-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container relative h-fit\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pb-32\",\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Profit and Loss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"13 Month Trailing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontSize: \"20px\",\n              color: \"black\",\n              fontWeight: 'lighter'\n            },\n            children: reportData === null || reportData === void 0 ? void 0 : reportData.companyName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full border-collapse text-sm mt-4 profit-loss-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"table-header-group\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"header-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"month-header\",\n                  style: {\n                    width: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), months.map((month, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-right text-white p-2 font-bold text-sm month-header\",\n                  style: {\n                    backgroundColor: headerBgColor,\n                    ...contentTextStyle,\n                    fontSize: '15px',\n                    color: 'white'\n                  },\n                  children: month\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"table-row-group\",\n              children: tableData.map((item, index) => renderTableRow(item, `data-${index}`))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 text-center text-slate-300 p-5 text-xs py-9\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this) : null;\n};\n_s(ProfitLoss13MonthDashboard, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ProfitLoss13MonthDashboard;\nexport default ProfitLoss13MonthDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProfitLoss13MonthDashboard\");", "map": {"version": 3, "names": ["useEffect", "jsxDEV", "_jsxDEV", "ProfitLoss13MonthDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "subHeaderTextStyle", "contentTextStyle", "contentSettings", "reportData", "_s", "headerBgColor", "color", "formatCurrency", "value", "numValue", "parseFloat", "replace", "isNaN", "Math", "abs", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "transformApiData", "apiData", "profitAndLossMonthsTrailing", "months", "tableData", "rawData", "length", "Object", "keys", "filter", "key", "groupedData", "reduce", "acc", "item", "accountType", "account_type", "push", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "category", "rowData", "map", "month", "isTotal", "account_name", "toLowerCase", "includes", "label", "data", "indented", "className", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "companyName", "renderTableRow", "index", "_", "i", "backgroundColor", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/MonthTrailing.jsx"], "sourcesContent": ["import { useEffect } from 'react';\r\n\r\nconst ProfitLoss13MonthDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  subHeaderTextStyle = {},\r\n  contentTextStyle = {},\r\n  contentSettings = null,\r\n  reportData = null // Add reportData prop to receive API data\r\n}) => {\r\n  // Extract background color from headerTextStyle for table header\r\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\r\n\r\n  // Function to format currency values with $ prefix and comma separators\r\n// Function to format currency values with $ prefix and comma separators\r\nconst formatCurrency = (value) => {\r\n  if (!value || value === '0' || value === 0) return '$0';\r\n  \r\n  // Convert to number if it's a string\r\n  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[,$]/g, '')) : value;\r\n  \r\n  // Check if the value is a valid number\r\n  if (isNaN(numValue)) return '$0';\r\n  \r\n  // Handle negative values correctly\r\n  if (numValue < 0) {\r\n    return '-$' + Math.abs(numValue).toLocaleString('en-US', {\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    });\r\n  }\r\n  \r\n  // Format positive values with commas and $ prefix\r\n  return '$' + numValue.toLocaleString('en-US', {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  });\r\n};\r\n\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Function to transform API data into the required format\r\n  const transformApiData = (apiData) => {\r\n    if (!apiData || !apiData.profitAndLossMonthsTrailing) {\r\n      return { months: [], tableData: [] };\r\n    }\r\n\r\n    const rawData = apiData.profitAndLossMonthsTrailing;\r\n\r\n    // Extract months from the first data item (excluding account_type and account_name)\r\n    const months = rawData.length > 0\r\n      ? Object.keys(rawData[0]).filter(key => key !== 'account_type' && key !== 'account_name')\r\n      : [];\r\n\r\n    // Group data by account_type\r\n    const groupedData = rawData.reduce((acc, item) => {\r\n      const accountType = item.account_type;\r\n      if (!acc[accountType]) {\r\n        acc[accountType] = [];\r\n      }\r\n      acc[accountType].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    // Transform grouped data into table format\r\n    const tableData = [];\r\n\r\n    Object.keys(groupedData).forEach(accountType => {\r\n      // Add header row for account type\r\n      tableData.push({\r\n        isHeader: true,\r\n        category: accountType\r\n      });\r\n\r\n      // Add data rows for this account type\r\n      groupedData[accountType].forEach(item => {\r\n        // Format the data with currency formatting\r\n        const rowData = months.map(month => formatCurrency(item[month] || '0'));\r\n\r\n        // Check if this is a total row (contains \"Total\" in account_name)\r\n        const isTotal = item.account_name.toLowerCase().includes('total');\r\n\r\n        tableData.push({\r\n          label: item.account_name,\r\n          data: rowData,\r\n          isTotal: isTotal,\r\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total')\r\n        });\r\n      });\r\n    });\r\n\r\n    return { months, tableData };\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (shouldDisplayChart('thirteenMonthTrailing')) {\r\n      transformApiData(reportData);\r\n    }\r\n  }, [reportData, contentSettings]);\r\n\r\n  // Transform the API data\r\n  const { months, tableData } = transformApiData(reportData);\r\n\r\n  // Fallback to empty state if no data\r\n  if (!reportData || !reportData.profitAndLossMonthsTrailing || months.length === 0) {\r\n    return (\r\n      <div className=\"p-5 section profit-loss-section month-trailing\">\r\n        <div className=\"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container relative\">\r\n          <div className=\"report-header\">\r\n            <h4 style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}>\r\n              Profit and Loss\r\n            </h4>\r\n            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              13 Month Trailing\r\n            </div>\r\n            <div style={{ ...subHeadingTextStyle, fontSize: \"20px\", color : \"black\", fontWeight: 'lighter' }}>\r\n              {reportData?.companyName}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg text-gray-600\">No data available</div>\r\n              <div className=\"text-sm text-gray-500 mt-2\">Please check your data source</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderTableRow = (item, index) => {\r\n    if (item.isHeader) {\r\n      return (\r\n        <tr key={index} className=\"font-bold text-gray-800\">\r\n          <td className=\"text-left pl-2 font-normal\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.category}</strong>\r\n          </td>\r\n          {months.map((_, i) => (\r\n            <td\r\n              key={i}\r\n              style={{\r\n                backgroundColor: i === months.length - 1 ? '#d2e9ea' : 'transparent'\r\n              }}\r\n            ></td>\r\n          ))}\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray\">\r\n          <td className=\"text-left pl-2 font-normal\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          {item.data.map((value, i) => (\r\n            <td\r\n              key={i}\r\n              className=\"text-right font-mono\"\r\n              style={{\r\n                ...contentTextStyle,\r\n                fontSize: '15px',\r\n                backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\r\n              }}\r\n            >\r\n              <strong>{value}</strong>\r\n            </td>\r\n          ))}\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <tr key={index}>\r\n        <td\r\n          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}\r\n          style={{ ...contentTextStyle, fontSize: '15px' }}\r\n        >\r\n          {item.label}\r\n        </td>\r\n        {item.data.map((value, i) => (\r\n          <td\r\n            key={i}\r\n            className=\"text-right font-mono\"\r\n            style={{\r\n              ...contentTextStyle,\r\n              fontSize: '15px',\r\n              backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\r\n            }}\r\n          >\r\n            {value}\r\n          </td>\r\n        ))}\r\n      </tr>\r\n    );\r\n  };\r\n\r\n  return (\r\n    (shouldDisplayChart('thirteenMonthTrailing')) ? (\r\n    <div className=\"p-5 section profit-loss-section\">\r\n      {/* Main Container with relative positioning and full height */}\r\n      <div className=\"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container relative h-fit\">\r\n        {/* Content Area - takes up available space minus disclaimer height */}\r\n        <div className=\"pb-32\"> {/* Add padding bottom to ensure content doesn't overlap with disclaimer */}\r\n          {/* Header Section */}\r\n          <div className=\"report-header\">\r\n            <h4\r\n              style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n            >\r\n              Profit and Loss\r\n            </h4>\r\n            <div\r\n              style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n            >\r\n              13 Month Trailing\r\n            </div>\r\n            <div\r\n              style={{ ...subHeadingTextStyle, fontSize: \"20px\", color : \"black\", fontWeight: 'lighter' }}\r\n            >\r\n              {reportData?.companyName}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Table */}\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"w-full border-collapse text-sm mt-4 profit-loss-table\">\r\n              <thead className=\"table-header-group\">\r\n                <tr className=\"header-row\">\r\n                  <th\r\n                    className=\"month-header\"\r\n                    style={{\r\n                      width: '200px',\r\n                    }}\r\n                  >\r\n\r\n                  </th>\r\n                  {months.map((month, index) => (\r\n                    <th\r\n                      key={index}\r\n                      className=\"text-right text-white p-2 font-bold text-sm month-header\"\r\n                      style={{\r\n                        backgroundColor: headerBgColor,\r\n                        ...contentTextStyle,\r\n                        fontSize: '15px',\r\n                        color: 'white'\r\n                      }}\r\n                    >\r\n                      {month}\r\n                    </th>\r\n                  ))}\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"table-row-group\">\r\n                {/* Render all transformed data */}\r\n                {tableData.map((item, index) => renderTableRow(item, `data-${index}`))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom disclaimer - positioned absolutely at the bottom */}\r\n        <div className='absolute bottom-0 left-0 right-0 text-center text-slate-300 p-5 text-xs py-9'>\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    ) : null\r\n  );\r\n};\r\n\r\nexport default ProfitLoss13MonthDashboard;"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,0BAA0B,GAAGA,CAAC;EAClCC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,kBAAkB,GAAG,CAAC,CAAC;EACvBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,eAAe,GAAG,IAAI;EACtBC,UAAU,GAAG,IAAI,CAAC;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAMC,aAAa,GAAGR,eAAe,CAACS,KAAK,IAAI,SAAS;;EAExD;EACF;EACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;;IAEvD;IACA,MAAMC,QAAQ,GAAG,OAAOD,KAAK,KAAK,QAAQ,GAAGE,UAAU,CAACF,KAAK,CAACG,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,GAAGH,KAAK;;IAE3F;IACA,IAAII,KAAK,CAACH,QAAQ,CAAC,EAAE,OAAO,IAAI;;IAEhC;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,IAAI,GAAGI,IAAI,CAACC,GAAG,CAACL,QAAQ,CAAC,CAACM,cAAc,CAAC,OAAO,EAAE;QACvDC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO,GAAG,GAAGR,QAAQ,CAACM,cAAc,CAAC,OAAO,EAAE;MAC5CC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC;EACJ,CAAC;EAEC,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACjB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOlB,eAAe,CAACkB,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,2BAA2B,EAAE;MACpD,OAAO;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC;IACtC;IAEA,MAAMC,OAAO,GAAGJ,OAAO,CAACC,2BAA2B;;IAEnD;IACA,MAAMC,MAAM,GAAGE,OAAO,CAACC,MAAM,GAAG,CAAC,GAC7BC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,cAAc,CAAC,GACvF,EAAE;;IAEN;IACA,MAAMC,WAAW,GAAGN,OAAO,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAChD,MAAMC,WAAW,GAAGD,IAAI,CAACE,YAAY;MACrC,IAAI,CAACH,GAAG,CAACE,WAAW,CAAC,EAAE;QACrBF,GAAG,CAACE,WAAW,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACE,WAAW,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC;MAC3B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMT,SAAS,GAAG,EAAE;IAEpBG,MAAM,CAACC,IAAI,CAACG,WAAW,CAAC,CAACO,OAAO,CAACH,WAAW,IAAI;MAC9C;MACAX,SAAS,CAACa,IAAI,CAAC;QACbE,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAEL;MACZ,CAAC,CAAC;;MAEF;MACAJ,WAAW,CAACI,WAAW,CAAC,CAACG,OAAO,CAACJ,IAAI,IAAI;QACvC;QACA,MAAMO,OAAO,GAAGlB,MAAM,CAACmB,GAAG,CAACC,KAAK,IAAIrC,cAAc,CAAC4B,IAAI,CAACS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;;QAEvE;QACA,MAAMC,OAAO,GAAGV,IAAI,CAACW,YAAY,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;QAEjEvB,SAAS,CAACa,IAAI,CAAC;UACbW,KAAK,EAAEd,IAAI,CAACW,YAAY;UACxBI,IAAI,EAAER,OAAO;UACbG,OAAO,EAAEA,OAAO;UAChBM,QAAQ,EAAE,CAACN,OAAO,IAAI,CAACV,IAAI,CAACW,YAAY,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO;QACzE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MAAExB,MAAM;MAAEC;IAAU,CAAC;EAC9B,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACd,IAAIyB,kBAAkB,CAAC,uBAAuB,CAAC,EAAE;MAC/CG,gBAAgB,CAAClB,UAAU,CAAC;IAC9B;EACF,CAAC,EAAE,CAACA,UAAU,EAAED,eAAe,CAAC,CAAC;;EAEjC;EACA,MAAM;IAAEsB,MAAM;IAAEC;EAAU,CAAC,GAAGJ,gBAAgB,CAAClB,UAAU,CAAC;;EAE1D;EACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACoB,2BAA2B,IAAIC,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACjF,oBACEhC,OAAA;MAAKyD,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7D1D,OAAA;QAAKyD,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAChG1D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAEwD,UAAU,EAAE;YAAU,CAAE;YAAAF,QAAA,EAAC;UAE9D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhE,OAAA;YAAK2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAEwD,UAAU,EAAE,SAAS;cAAEjD,KAAK,EAAE;YAAQ,CAAE;YAAA+C,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhE,OAAA;YAAK2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAE6D,QAAQ,EAAE,MAAM;cAAEtD,KAAK,EAAG,OAAO;cAAEiD,UAAU,EAAE;YAAU,CAAE;YAAAF,QAAA,EAC9FlD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0D;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA;UAAKyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD1D,OAAA;YAAKyD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B1D,OAAA;cAAKyD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DhE,OAAA;cAAKyD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMG,cAAc,GAAGA,CAAC3B,IAAI,EAAE4B,KAAK,KAAK;IACtC,IAAI5B,IAAI,CAACK,QAAQ,EAAE;MACjB,oBACE7C,OAAA;QAAgByD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACjD1D,OAAA;UAAIyD,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAE,GAAGrD,gBAAgB;YAAE2D,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1F1D,OAAA;YAAA0D,QAAA,EAASlB,IAAI,CAACM;UAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACJnC,MAAM,CAACmB,GAAG,CAAC,CAACqB,CAAC,EAAEC,CAAC,kBACftE,OAAA;UAEE2D,KAAK,EAAE;YACLY,eAAe,EAAED,CAAC,KAAKzC,MAAM,CAACG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;UACzD;QAAE,GAHGsC,CAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIH,CACN,CAAC;MAAA,GAXKI,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYV,CAAC;IAET;IAEA,IAAIxB,IAAI,CAACU,OAAO,EAAE;MAChB,oBACElD,OAAA;QAAgByD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAChD1D,OAAA;UAAIyD,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAE,GAAGrD,gBAAgB;YAAE2D,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1F1D,OAAA;YAAA0D,QAAA,EAASlB,IAAI,CAACc;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,EACJxB,IAAI,CAACe,IAAI,CAACP,GAAG,CAAC,CAACnC,KAAK,EAAEyD,CAAC,kBACtBtE,OAAA;UAEEyD,SAAS,EAAC,sBAAsB;UAChCE,KAAK,EAAE;YACL,GAAGrD,gBAAgB;YACnB2D,QAAQ,EAAE,MAAM;YAChBM,eAAe,EAAED,CAAC,KAAK9B,IAAI,CAACe,IAAI,CAACvB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;UAC5D,CAAE;UAAA0B,QAAA,eAEF1D,OAAA;YAAA0D,QAAA,EAAS7C;UAAK;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC,GARnBM,CAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACL,CAAC;MAAA,GAhBKI,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBV,CAAC;IAET;IAEA,oBACEhE,OAAA;MAAA0D,QAAA,gBACE1D,OAAA;QACEyD,SAAS,EAAE,yBAAyBjB,IAAI,CAACgB,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAG;QACtEG,KAAK,EAAE;UAAE,GAAGrD,gBAAgB;UAAE2D,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAEhDlB,IAAI,CAACc;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EACJxB,IAAI,CAACe,IAAI,CAACP,GAAG,CAAC,CAACnC,KAAK,EAAEyD,CAAC,kBACtBtE,OAAA;QAEEyD,SAAS,EAAC,sBAAsB;QAChCE,KAAK,EAAE;UACL,GAAGrD,gBAAgB;UACnB2D,QAAQ,EAAE,MAAM;UAChBM,eAAe,EAAED,CAAC,KAAK9B,IAAI,CAACe,IAAI,CAACvB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;QAC5D,CAAE;QAAA0B,QAAA,EAED7C;MAAK,GARDyD,CAAC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASJ,CACL,CAAC;IAAA,GAnBKI,KAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoBV,CAAC;EAET,CAAC;EAED,OACGzC,kBAAkB,CAAC,uBAAuB,CAAC,gBAC5CvB,OAAA;IAAKyD,SAAS,EAAC,iCAAiC;IAAAC,QAAA,eAE9C1D,OAAA;MAAKyD,SAAS,EAAC,yFAAyF;MAAAC,QAAA,gBAEtG1D,OAAA;QAAKyD,SAAS,EAAC,OAAO;QAAAC,QAAA,GAAC,GAAC,eAEtB1D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1D,OAAA;YACE2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAEwD,UAAU,EAAE,SAAS;cAAEjD,KAAK,EAAE;YAAQ,CAAE;YAAA+C,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhE,OAAA;YACE2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAEwD,UAAU,EAAE,SAAS;cAAEjD,KAAK,EAAE;YAAQ,CAAE;YAAA+C,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhE,OAAA;YACE2D,KAAK,EAAE;cAAE,GAAGvD,mBAAmB;cAAE6D,QAAQ,EAAE,MAAM;cAAEtD,KAAK,EAAG,OAAO;cAAEiD,UAAU,EAAE;YAAU,CAAE;YAAAF,QAAA,EAE3FlD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0D;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhE,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA;YAAOyD,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBACtE1D,OAAA;cAAOyD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACnC1D,OAAA;gBAAIyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxB1D,OAAA;kBACEyD,SAAS,EAAC,cAAc;kBACxBE,KAAK,EAAE;oBACLa,KAAK,EAAE;kBACT;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGA,CAAC,EACJnC,MAAM,CAACmB,GAAG,CAAC,CAACC,KAAK,EAAEmB,KAAK,kBACvBpE,OAAA;kBAEEyD,SAAS,EAAC,0DAA0D;kBACpEE,KAAK,EAAE;oBACLY,eAAe,EAAE7D,aAAa;oBAC9B,GAAGJ,gBAAgB;oBACnB2D,QAAQ,EAAE,MAAM;oBAChBtD,KAAK,EAAE;kBACT,CAAE;kBAAA+C,QAAA,EAEDT;gBAAK,GATDmB,KAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUR,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhE,OAAA;cAAOyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAE/B5B,SAAS,CAACkB,GAAG,CAAC,CAACR,IAAI,EAAE4B,KAAK,KAAKD,cAAc,CAAC3B,IAAI,EAAE,QAAQ4B,KAAK,EAAE,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhE,OAAA;QAAKyD,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAC3F1D,OAAA;UAAA0D,QAAA,EAAG;QAKH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,GACF,IAAI;AAEZ,CAAC;AAACvD,EAAA,CApRIR,0BAA0B;AAAAwE,EAAA,GAA1BxE,0BAA0B;AAsRhC,eAAeA,0BAA0B;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}