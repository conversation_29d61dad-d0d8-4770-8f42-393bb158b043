{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\LiquiditySummary.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport ApexCharts from 'apexcharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LiquiditySummaryDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  contentSettings = null,\n  liquidityData = null\n}) => {\n  _s();\n  const netChangeRef = useRef(null);\n  const quickRatioRef = useRef(null);\n  const monthsCashRef = useRef(null);\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Enhanced data validation function\n  // Replace your isDataLoaded function with this improved version:\n\n  const isDataLoaded = () => {\n    if (!liquidityData) {\n      return false;\n    }\n    // Function to check if an array has meaningful data\n    const hasValidData = arr => {\n      return Array.isArray(arr) && arr.length > 0 && arr.some(item => item !== null && item !== undefined);\n    };\n    return liquidityData && (liquidityData.hasOwnProperty('netChangeInCash') || liquidityData.hasOwnProperty('quickRatio') || liquidityData.hasOwnProperty('monthOnCash'));\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      initializeCharts();\n    }\n  }, [liquidityData, contentSettings]);\n  const formatMonthYear = (year, month) => {\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  const initializeCharts = () => {\n    var _dataToUse$netChangeI, _dataToUse$netChangeI2, _dataToUse$quickRatio, _dataToUse$monthOnCas;\n    if (!liquidityData) return;\n\n    // Try to find the correct data structure\n    let dataToUse = liquidityData;\n\n    // Prepare data from API response\n    const categories = ((_dataToUse$netChangeI = dataToUse.netChangeInCash) === null || _dataToUse$netChangeI === void 0 ? void 0 : _dataToUse$netChangeI.map(item => formatMonthYear(item.year, item.month))) || [];\n    const netChangeInCashData = ((_dataToUse$netChangeI2 = dataToUse.netChangeInCash) === null || _dataToUse$netChangeI2 === void 0 ? void 0 : _dataToUse$netChangeI2.map(item => parseFloat(item.net_change_in_cash) / 1000 || 0 // Convert to thousands for better display\n    )) || [];\n    const quickRatioData = ((_dataToUse$quickRatio = dataToUse.quickRatio) === null || _dataToUse$quickRatio === void 0 ? void 0 : _dataToUse$quickRatio.map(item => parseFloat(item.quickRatio) || 0)) || [];\n    const monthsCashOnHandData = ((_dataToUse$monthOnCas = dataToUse.monthOnCash) === null || _dataToUse$monthOnCas === void 0 ? void 0 : _dataToUse$monthOnCas.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n\n    // 1. Net Change in Cash Chart\n    const netChangeOptions = {\n      series: [{\n        name: 'Net Change in Cash',\n        data: netChangeInCashData\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return '$' + val.toFixed(1) + 'k';\n          } else if (val >= 0.1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val <= -0.1) {\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\n          } else {\n            return '$' + val.toFixed(2) + 'k';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3 // Changed from 2 to 3 to match netProfitMarginOptions\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netChangeInCashData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netChangeInCashData.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default line color\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            colorBelowThreshold: '#d70015'\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return '$' + val.toFixed(1) + ' thousand';\n            } else if (val >= 0.1) {\n              return '$' + val.toFixed(2) + ' thousand';\n            } else if (val <= -0.1) {\n              return '-$' + Math.abs(val).toFixed(2) + ' thousand';\n            } else {\n              return '$' + val.toFixed(3) + ' thousand';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n    // 2. Quick Ratio Chart\n    const quickRatioOptions = {\n      series: [{\n        name: 'Quick Ratio',\n        data: quickRatioData\n      }],\n      chart: {\n        type: 'area',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: ['#5457a3']\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: '#5457a3',\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: '#5457a3',\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: ['#5457a3'],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#5457a3'],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2);\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      }\n    };\n\n    // 3. Months Cash on Hand Chart\n    const monthsCashOptions = {\n      series: [{\n        name: 'Months Cash on Hand',\n        data: monthsCashOnHandData\n      }],\n      chart: {\n        type: 'area',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: ['#298478']\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: '#298478',\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: '#298478',\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: ['#298478'],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#298478'],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2) + ' months';\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n\n              // Store chart instances globally for export\n              if (chartName === \"Net Change in Cash\") {\n                window.netChangeChart = chart;\n              } else if (chartName === \"Quick Ratio\") {\n                window.quickRatioChart = chart;\n              } else if (chartName === \"Months Cash on Hand\") {\n                window.monthsCashChart = chart;\n              }\n            } catch (error) {\n              console.error(`LiquiditySummary - Error rendering ${chartName} chart:`, error);\n            }\n          }\n        }, 10);\n      }\n    };\n    if (netChangeInCashData.length > 0) {\n      clearAndRenderChart(netChangeRef, netChangeOptions, 'Net Change in Cash');\n    } else if (netChangeRef.current) {\n      netChangeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful net change in cash data available</div>';\n    }\n    if (quickRatioData.length > 0) {\n      clearAndRenderChart(quickRatioRef, quickRatioOptions, 'Quick Ratio');\n    } else if (quickRatioRef.current) {\n      quickRatioRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful quick ratio data available</div>';\n    }\n    if (monthsCashOnHandData.length > 0) {\n      clearAndRenderChart(monthsCashRef, monthsCashOptions, 'Months Cash on Hand');\n    } else if (monthsCashRef.current) {\n      monthsCashRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful months cash on hand data available</div>';\n    }\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n\n  // Function to determine which charts should be rendered and their order\n  const getEnabledCharts = () => {\n    const allCharts = [{\n      key: 'netChangeInCash',\n      title: 'Net Change in Cash',\n      ref: netChangeRef,\n      hasDescription: false\n    }, {\n      key: 'quickRatio',\n      title: 'Quick Ratio',\n      ref: quickRatioRef,\n      hasDescription: false\n    }, {\n      key: 'monthsCashOnHand',\n      title: 'Months Cash on Hand',\n      ref: monthsCashRef,\n      hasDescription: true,\n      description: {\n        title: 'Months Cash on Hand',\n        content: 'Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.'\n      }\n    }];\n\n    // Filter charts based on settings\n    return allCharts.filter(chart => shouldDisplayChart(chart.key));\n  };\n\n  // Get enabled charts for dynamic layout\n  const enabledCharts = getEnabledCharts();\n\n  // Helper function to render a chart component\n  const renderChart = chart => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 border-b-4 border-blue-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-2xl font-semibold text-teal-600 mb-5\",\n      style: subHeadingTextStyle,\n      children: chart.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chart.ref\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), chart.hasDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-teal-600 text-2xl\",\n        style: {\n          ...subHeadingTextStyle,\n          fontWeight: 'lighter'\n        },\n        children: chart.description.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentTextStyle,\n        children: chart.description.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 9\n    }, this)]\n  }, chart.key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 510,\n    columnNumber: 5\n  }, this);\n\n  // Don't render anything if no charts are enabled\n  if (enabledCharts.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5 min-h-fit\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl  mx-auto bg-white flex flex-col gap-10 p-10 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Liquidity Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(liquidityData === null || liquidityData === void 0 ? void 0 : liquidityData.FYEndYear, liquidityData === null || liquidityData === void 0 ? void 0 : liquidityData.FYStartMonth), \" | \", formatCompanyName(liquidityData === null || liquidityData === void 0 ? void 0 : liquidityData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), enabledCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 540,\n    columnNumber: 5\n  }, this);\n};\n_s(LiquiditySummaryDashboard, \"68uIpoO+VVhsTFN3ehX92Vi/wyU=\");\n_c = LiquiditySummaryDashboard;\nexport default LiquiditySummaryDashboard;\nvar _c;\n$RefreshReg$(_c, \"LiquiditySummaryDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LiquiditySummaryDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "contentSettings", "liquidityData", "_s", "netChangeRef", "quickRatioRef", "monthsCashRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "isDataLoaded", "hasValidData", "arr", "Array", "isArray", "length", "some", "item", "undefined", "hasOwnProperty", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "_dataToUse$netChangeI", "_dataToUse$netChangeI2", "_dataToUse$quickRatio", "_dataToUse$monthOnCas", "dataToUse", "categories", "netChangeInCash", "map", "netChangeInCashData", "parseFloat", "net_change_in_cash", "quickRatioData", "quickRatio", "monthsCashOnHandData", "monthOnCash", "value", "monthsOfCash", "months_cash", "monthsCashOnHand", "netChangeOptions", "series", "name", "data", "chart", "type", "height", "toolbar", "show", "background", "zoom", "enabled", "dataLabels", "formatter", "val", "toFixed", "Math", "abs", "style", "fontSize", "colors", "fontWeight", "offsetY", "dropShadow", "stroke", "curve", "width", "fill", "markers", "size", "strokeColors", "strokeWidth", "hover", "discrete", "index", "seriesIndex", "dataPointIndex", "fillColor", "strokeColor", "xaxis", "labels", "axisBorder", "axisTicks", "yaxis", "plotOptions", "line", "threshold", "colorAboveThreshold", "colorBelowThreshold", "tooltip", "y", "grid", "padding", "left", "right", "top", "bottom", "annotations", "borderColor", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "quickRatioOptions", "gradient", "shadeIntensity", "colorStops", "offset", "color", "legend", "monthsCashOptions", "clearAndRender<PERSON>hart", "ref", "options", "chartName", "current", "innerHTML", "setTimeout", "render", "window", "netChangeChart", "quickRatioChart", "months<PERSON>ash<PERSON><PERSON>", "error", "console", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "formatHeaderStyle", "parseInt", "formatCompanyName", "companyName", "substring", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "all<PERSON>hart<PERSON>", "key", "title", "hasDescription", "description", "content", "filter", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYEndYear", "FYStartMonth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/LiquiditySummary.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport ApexCharts from 'apexcharts';\r\n\r\nconst LiquiditySummaryDashboard = ({\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  contentSettings = null,\r\n  liquidityData = null\r\n}) => {\r\n  const netChangeRef = useRef(null);\r\n  const quickRatioRef = useRef(null);\r\n  const monthsCashRef = useRef(null);\r\n\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Enhanced data validation function\r\n  // Replace your isDataLoaded function with this improved version:\r\n\r\n  const isDataLoaded = () => {\r\n    if (!liquidityData) {\r\n      return false;\r\n    }\r\n    // Function to check if an array has meaningful data\r\n    const hasValidData = (arr) => {\r\n      return Array.isArray(arr) &&\r\n        arr.length > 0 &&\r\n        arr.some(item => item !== null && item !== undefined);\r\n    };\r\n\r\n    return liquidityData && (\r\n      liquidityData.hasOwnProperty('netChangeInCash') ||\r\n      liquidityData.hasOwnProperty('quickRatio') ||\r\n      liquidityData.hasOwnProperty('monthOnCash')\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      initializeCharts();\r\n    }\r\n  }, [liquidityData, contentSettings]);\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\r\n      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    if (!liquidityData) return;\r\n\r\n    // Try to find the correct data structure\r\n    let dataToUse = liquidityData;\r\n\r\n    // Prepare data from API response\r\n    const categories = dataToUse.netChangeInCash?.map(item =>\r\n      formatMonthYear(item.year, item.month)\r\n    ) || [];\r\n\r\n    const netChangeInCashData = dataToUse.netChangeInCash?.map(item =>\r\n      parseFloat(item.net_change_in_cash) / 1000 || 0  // Convert to thousands for better display\r\n    ) || [];\r\n\r\n    const quickRatioData = dataToUse.quickRatio?.map(item =>\r\n      parseFloat(item.quickRatio) || 0\r\n    ) || [];\r\n\r\n    const monthsCashOnHandData = dataToUse.monthOnCash?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n\r\n    // 1. Net Change in Cash Chart\r\n    const netChangeOptions = {\r\n      series: [{\r\n        name: 'Net Change in Cash',\r\n        data: netChangeInCashData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return '$' + val.toFixed(1) + 'k';\r\n          } else if (val >= 0.1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val <= -0.1) {\r\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\r\n          } else {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3 // Changed from 2 to 3 to match netProfitMarginOptions\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netChangeInCashData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netChangeInCashData.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default line color\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C',\r\n            colorBelowThreshold: '#d70015',\r\n          },\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return '$' + val.toFixed(1) + ' thousand';\r\n            } else if (val >= 0.1) {\r\n              return '$' + val.toFixed(2) + ' thousand';\r\n            } else if (val <= -0.1) {\r\n              return '-$' + Math.abs(val).toFixed(2) + ' thousand';\r\n            } else {\r\n              return '$' + val.toFixed(3) + ' thousand';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n    // 2. Quick Ratio Chart\r\n    const quickRatioOptions = {\r\n      series: [{\r\n        name: 'Quick Ratio',\r\n        data: quickRatioData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: ['#5457a3']\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: '#5457a3', opacity: 0.4 },\r\n            { offset: 100, color: '#5457a3', opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: ['#5457a3'],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#5457a3'],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return val.toFixed(2);\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      }\r\n    };\r\n\r\n    // 3. Months Cash on Hand Chart\r\n    const monthsCashOptions = {\r\n      series: [{\r\n        name: 'Months Cash on Hand',\r\n        data: monthsCashOnHandData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: ['#298478']\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: '#298478', opacity: 0.4 },\r\n            { offset: 100, color: '#298478', opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: ['#298478'],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#298478'],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return val.toFixed(2) + ' months';\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      }\r\n    };\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n\r\n              // Store chart instances globally for export\r\n              if (chartName === \"Net Change in Cash\") {\r\n                window.netChangeChart = chart;\r\n              } else if (chartName === \"Quick Ratio\") {\r\n                window.quickRatioChart = chart;\r\n              } else if (chartName === \"Months Cash on Hand\") {\r\n                window.monthsCashChart = chart;\r\n              }\r\n            } catch (error) {\r\n              console.error(`LiquiditySummary - Error rendering ${chartName} chart:`, error);\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    if (netChangeInCashData.length > 0) {\r\n      clearAndRenderChart(netChangeRef, netChangeOptions, 'Net Change in Cash');\r\n    } else if (netChangeRef.current) {\r\n      netChangeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful net change in cash data available</div>';\r\n    }\r\n\r\n    if (quickRatioData.length > 0) {\r\n      clearAndRenderChart(quickRatioRef, quickRatioOptions, 'Quick Ratio');\r\n    } else if (quickRatioRef.current) {\r\n      quickRatioRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful quick ratio data available</div>';\r\n    }\r\n\r\n    if (monthsCashOnHandData.length > 0) {\r\n      clearAndRenderChart(monthsCashRef, monthsCashOptions, 'Months Cash on Hand');\r\n    } else if (monthsCashRef.current) {\r\n      monthsCashRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful months cash on hand data available</div>';\r\n    }\r\n  };\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n\r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n\r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n\r\n    return companyName;\r\n  };\r\n\r\n  // Function to determine which charts should be rendered and their order\r\n  const getEnabledCharts = () => {\r\n    const allCharts = [\r\n      {\r\n        key: 'netChangeInCash',\r\n        title: 'Net Change in Cash',\r\n        ref: netChangeRef,\r\n        hasDescription: false\r\n      },\r\n      {\r\n        key: 'quickRatio',\r\n        title: 'Quick Ratio',\r\n        ref: quickRatioRef,\r\n        hasDescription: false\r\n      },\r\n      {\r\n        key: 'monthsCashOnHand',\r\n        title: 'Months Cash on Hand',\r\n        ref: monthsCashRef,\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Months Cash on Hand',\r\n          content: 'Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.'\r\n        }\r\n      }\r\n    ];\r\n\r\n    // Filter charts based on settings\r\n    return allCharts.filter(chart =>\r\n      shouldDisplayChart(chart.key)\r\n    );\r\n  };\r\n\r\n  // Get enabled charts for dynamic layout\r\n  const enabledCharts = getEnabledCharts();\r\n\r\n  // Helper function to render a chart component\r\n  const renderChart = (chart) => (\r\n    <div key={chart.key} className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n      <div\r\n        className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n        style={subHeadingTextStyle}\r\n      >\r\n        {chart.title}\r\n      </div>\r\n      <div ref={chart.ref}></div>\r\n      {chart.hasDescription && (\r\n        <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n          <div\r\n            className=\"text-teal-600 text-2xl\"\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}\r\n          >\r\n            {chart.description.title}\r\n          </div>\r\n          <div style={contentTextStyle}>\r\n            {chart.description.content}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  // Don't render anything if no charts are enabled\r\n  if (enabledCharts.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-5 min-h-fit\">\r\n      <div className=\"max-w-6xl  mx-auto bg-white flex flex-col gap-10 p-10 mb-4\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Liquidity Summary\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(liquidityData?.FYEndYear, liquidityData?.FYStartMonth)} | {formatCompanyName(liquidityData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Render enabled charts dynamically */}\r\n        {enabledCharts.map(chart => renderChart(chart))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LiquiditySummaryDashboard"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,yBAAyB,GAAGA,CAAC;EACjCC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,eAAe,GAAG,IAAI;EACtBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,YAAY,GAAGX,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMY,aAAa,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMa,aAAa,GAAGb,MAAM,CAAC,IAAI,CAAC;EAElC,MAAMc,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACP,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEQ,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOR,eAAe,CAACQ,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA;;EAEA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACR,aAAa,EAAE;MAClB,OAAO,KAAK;IACd;IACA;IACA,MAAMS,YAAY,GAAIC,GAAG,IAAK;MAC5B,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IACvBA,GAAG,CAACG,MAAM,GAAG,CAAC,IACdH,GAAG,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKC,SAAS,CAAC;IACzD,CAAC;IAED,OAAOhB,aAAa,KAClBA,aAAa,CAACiB,cAAc,CAAC,iBAAiB,CAAC,IAC/CjB,aAAa,CAACiB,cAAc,CAAC,YAAY,CAAC,IAC1CjB,aAAa,CAACiB,cAAc,CAAC,aAAa,CAAC,CAC5C;EACH,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd,IAAIkB,YAAY,CAAC,CAAC,EAAE;MAClBU,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClB,aAAa,EAAED,eAAe,CAAC,CAAC;EAEpC,MAAMoB,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC1D,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3C,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,MAAMN,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAO,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC7B,IAAI,CAAC5B,aAAa,EAAE;;IAEpB;IACA,IAAI6B,SAAS,GAAG7B,aAAa;;IAE7B;IACA,MAAM8B,UAAU,GAAG,EAAAL,qBAAA,GAAAI,SAAS,CAACE,eAAe,cAAAN,qBAAA,uBAAzBA,qBAAA,CAA2BO,GAAG,CAACjB,IAAI,IACpDI,eAAe,CAACJ,IAAI,CAACK,IAAI,EAAEL,IAAI,CAACM,KAAK,CACvC,CAAC,KAAI,EAAE;IAEP,MAAMY,mBAAmB,GAAG,EAAAP,sBAAA,GAAAG,SAAS,CAACE,eAAe,cAAAL,sBAAA,uBAAzBA,sBAAA,CAA2BM,GAAG,CAACjB,IAAI,IAC7DmB,UAAU,CAACnB,IAAI,CAACoB,kBAAkB,CAAC,GAAG,IAAI,IAAI,CAAC,CAAE;IACnD,CAAC,KAAI,EAAE;IAEP,MAAMC,cAAc,GAAG,EAAAT,qBAAA,GAAAE,SAAS,CAACQ,UAAU,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBK,GAAG,CAACjB,IAAI,IACnDmB,UAAU,CAACnB,IAAI,CAACsB,UAAU,CAAC,IAAI,CACjC,CAAC,KAAI,EAAE;IAEP,MAAMC,oBAAoB,GAAG,EAAAV,qBAAA,GAAAC,SAAS,CAACU,WAAW,cAAAX,qBAAA,uBAArBA,qBAAA,CAAuBI,GAAG,CAACjB,IAAI,IAAI;MAC9D;MACA,MAAMyB,KAAK,GAAGzB,IAAI,CAAC0B,YAAY,IAAI1B,IAAI,CAAC2B,WAAW,IAAI3B,IAAI,CAAC4B,gBAAgB,IAAI,CAAC;MACjF,OAAOT,UAAU,CAACM,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;;IAGR;IACA,MAAMI,gBAAgB,GAAG;MACvBC,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAEd;MACR,CAAC,CAAC;MACFe,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAID,GAAG,IAAI,GAAG,EAAE;YACrB,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAID,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC9C,CAAC,MAAM;YACL,OAAO,GAAG,GAAGD,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC;QACF,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZb,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDY,UAAU,EAAE;UACVZ,OAAO,EAAE;QACX;MACF,CAAC;MACDa,MAAM,EAAE;QACNC,KAAK,EAAE,UAAU;QACjBC,KAAK,EAAE,CAAC,CAAC;MACX,CAAC;MACDC,IAAI,EAAE;QACJtB,IAAI,EAAE;MACR,CAAC;MACDuB,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdX,MAAM,EAAE/B,mBAAmB,CAACD,GAAG,CAAC0B,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACxEkB,KAAK,EAAE;UACLH,IAAI,EAAE;QACR,CAAC;QACDI,QAAQ,EAAE5C,mBAAmB,CAACD,GAAG,CAAC,CAAC0B,GAAG,EAAEoB,KAAK,MAAM;UACjDC,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEF,KAAK;UACrBG,SAAS,EAAEvB,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CwB,WAAW,EAAE,MAAM;UACnBT,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDU,KAAK,EAAE;QACLrD,UAAU,EAAEA,UAAU;QACtBsD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEjC,IAAI,EAAE;QAAM,CAAC;QAC3BkC,SAAS,EAAE;UAAElC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDmC,KAAK,EAAE;QACLnC,IAAI,EAAE;MACR,CAAC;MACDY,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBwB,WAAW,EAAE;QACXC,IAAI,EAAE;UACJzB,MAAM,EAAE;YACN0B,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAC9BC,mBAAmB,EAAE;UACvB;QACF;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C,CAAC,MAAM,IAAID,GAAG,IAAI,GAAG,EAAE;cACrB,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C,CAAC,MAAM,IAAID,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YACtD,CAAC,MAAM;cACL,OAAO,GAAG,GAAGD,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C;UACF;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ3C,IAAI,EAAE,KAAK;QACX4C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXd,KAAK,EAAE,CAAC;UACNO,CAAC,EAAE,CAAC;UACJQ,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IACD;IACA,MAAMC,iBAAiB,GAAG;MACxB7D,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAEX;MACR,CAAC,CAAC;MACFY,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZb,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDY,UAAU,EAAE;UACVZ,OAAO,EAAE;QACX;MACF,CAAC;MACDa,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACRN,MAAM,EAAE,CAAC,SAAS;MACpB,CAAC;MACDO,IAAI,EAAE;QACJtB,IAAI,EAAE,UAAU;QAChB0D,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjB3D,IAAI,EAAE,UAAU;UAChB4D,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC,EAC7C;YAAEK,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC;QAEnD;MACF,CAAC;MACDjC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPT,MAAM,EAAE,CAAC,SAAS,CAAC;QACnBU,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDU,KAAK,EAAE;QACLrD,UAAU,EAAEA,UAAU;QACtBsD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEjC,IAAI,EAAE;QAAM,CAAC;QAC3BkC,SAAS,EAAE;UAAElC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDmC,KAAK,EAAE;QACLnC,IAAI,EAAE;MACR,CAAC;MACDY,MAAM,EAAE,CAAC,SAAS,CAAC;MACnB6B,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;UACvB;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ3C,IAAI,EAAE,KAAK;QACX4C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDY,MAAM,EAAE;QACN5D,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAM6D,iBAAiB,GAAG;MACxBpE,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,qBAAqB;QAC3BC,IAAI,EAAET;MACR,CAAC,CAAC;MACFU,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZb,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDY,UAAU,EAAE;UACVZ,OAAO,EAAE;QACX;MACF,CAAC;MACDa,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACRN,MAAM,EAAE,CAAC,SAAS;MACpB,CAAC;MACDO,IAAI,EAAE;QACJtB,IAAI,EAAE,UAAU;QAChB0D,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjB3D,IAAI,EAAE,UAAU;UAChB4D,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC,EAC7C;YAAEK,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC;QAEnD;MACF,CAAC;MACDjC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPT,MAAM,EAAE,CAAC,SAAS,CAAC;QACnBU,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDU,KAAK,EAAE;QACLrD,UAAU,EAAEA,UAAU;QACtBsD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEjC,IAAI,EAAE;QAAM,CAAC;QAC3BkC,SAAS,EAAE;UAAElC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDmC,KAAK,EAAE;QACLnC,IAAI,EAAE;MACR,CAAC;MACDY,MAAM,EAAE,CAAC,SAAS,CAAC;MACnB6B,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS;UACnC;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ3C,IAAI,EAAE,KAAK;QACX4C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDY,MAAM,EAAE;QACN5D,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAM8D,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,OAAO,EAAEC,SAAS,KAAK;MACvD,IAAIF,GAAG,CAACG,OAAO,EAAE;QACf;QACAH,GAAG,CAACG,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACf,IAAIL,GAAG,CAACG,OAAO,EAAE;YACf,IAAI;cACF,MAAMtE,KAAK,GAAG,IAAIxD,UAAU,CAAC2H,GAAG,CAACG,OAAO,EAAEF,OAAO,CAAC;cAClDpE,KAAK,CAACyE,MAAM,CAAC,CAAC;;cAEd;cACA,IAAIJ,SAAS,KAAK,oBAAoB,EAAE;gBACtCK,MAAM,CAACC,cAAc,GAAG3E,KAAK;cAC/B,CAAC,MAAM,IAAIqE,SAAS,KAAK,aAAa,EAAE;gBACtCK,MAAM,CAACE,eAAe,GAAG5E,KAAK;cAChC,CAAC,MAAM,IAAIqE,SAAS,KAAK,qBAAqB,EAAE;gBAC9CK,MAAM,CAACG,eAAe,GAAG7E,KAAK;cAChC;YACF,CAAC,CAAC,OAAO8E,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,sCAAsCT,SAAS,SAAS,EAAES,KAAK,CAAC;YAChF;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;IAED,IAAI7F,mBAAmB,CAACpB,MAAM,GAAG,CAAC,EAAE;MAClCqG,mBAAmB,CAAChH,YAAY,EAAE0C,gBAAgB,EAAE,oBAAoB,CAAC;IAC3E,CAAC,MAAM,IAAI1C,YAAY,CAACoH,OAAO,EAAE;MAC/BpH,YAAY,CAACoH,OAAO,CAACC,SAAS,GAAG,wHAAwH;IAC3J;IAEA,IAAInF,cAAc,CAACvB,MAAM,GAAG,CAAC,EAAE;MAC7BqG,mBAAmB,CAAC/G,aAAa,EAAEuG,iBAAiB,EAAE,aAAa,CAAC;IACtE,CAAC,MAAM,IAAIvG,aAAa,CAACmH,OAAO,EAAE;MAChCnH,aAAa,CAACmH,OAAO,CAACC,SAAS,GAAG,iHAAiH;IACrJ;IAEA,IAAIjF,oBAAoB,CAACzB,MAAM,GAAG,CAAC,EAAE;MACnCqG,mBAAmB,CAAC9G,aAAa,EAAE6G,iBAAiB,EAAE,qBAAqB,CAAC;IAC9E,CAAC,MAAM,IAAI7G,aAAa,CAACkH,OAAO,EAAE;MAChClH,aAAa,CAACkH,OAAO,CAACC,SAAS,GAAG,yHAAyH;IAC7J;EACF,CAAC;EAED,MAAMS,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAM5G,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAAC2G,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAG7G,UAAU,CAAC4G,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMtE,KAAK,GAAG;MAAE,GAAGlE;IAAgB,CAAC;IAEpC,IAAIkE,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAGsE,QAAQ,CAACvE,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,MAAMwE,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAAC1H,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAO0H,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,CAChB;MACEC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,oBAAoB;MAC3BzB,GAAG,EAAEjH,YAAY;MACjB2I,cAAc,EAAE;IAClB,CAAC,EACD;MACEF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,aAAa;MACpBzB,GAAG,EAAEhH,aAAa;MAClB0I,cAAc,EAAE;IAClB,CAAC,EACD;MACEF,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE,qBAAqB;MAC5BzB,GAAG,EAAE/G,aAAa;MAClByI,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXF,KAAK,EAAE,qBAAqB;QAC5BG,OAAO,EAAE;MACX;IACF,CAAC,CACF;;IAED;IACA,OAAOL,SAAS,CAACM,MAAM,CAAChG,KAAK,IAC3B3C,kBAAkB,CAAC2C,KAAK,CAAC2F,GAAG,CAC9B,CAAC;EACH,CAAC;;EAED;EACA,MAAMM,aAAa,GAAGR,gBAAgB,CAAC,CAAC;;EAExC;EACA,MAAMS,WAAW,GAAIlG,KAAK,iBACxBtD,OAAA;IAAqByJ,SAAS,EAAC,yCAAyC;IAAAC,QAAA,gBACtE1J,OAAA;MACEyJ,SAAS,EAAC,2CAA2C;MACrDrF,KAAK,EAAEjE,mBAAoB;MAAAuJ,QAAA,EAE1BpG,KAAK,CAAC4F;IAAK;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACN9J,OAAA;MAAKyH,GAAG,EAAEnE,KAAK,CAACmE;IAAI;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC1BxG,KAAK,CAAC6F,cAAc,iBACnBnJ,OAAA;MAAKyJ,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzE1J,OAAA;QACEyJ,SAAS,EAAC,wBAAwB;QAClCrF,KAAK,EAAE;UAAE,GAAGjE,mBAAmB;UAAEoE,UAAU,EAAE;QAAU,CAAE;QAAAmF,QAAA,EAExDpG,KAAK,CAAC8F,WAAW,CAACF;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACN9J,OAAA;QAAKoE,KAAK,EAAEhE,gBAAiB;QAAAsJ,QAAA,EAC1BpG,KAAK,CAAC8F,WAAW,CAACC;MAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,GApBOxG,KAAK,CAAC2F,GAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqBd,CACN;;EAED;EACA,IAAIP,aAAa,CAACpI,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,oBACEnB,OAAA;IAAKyJ,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1J,OAAA;MAAKyJ,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBAEzE1J,OAAA;QAAKyJ,SAAS,EAAC,+FAA+F;QAAAC,QAAA,gBAC5G1J,OAAA;UACEyJ,SAAS,EAAC,sCAAsC;UAChDrF,KAAK,EAAElE,eAAgB;UAAAwJ,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9J,OAAA;UAAGyJ,SAAS,EAAC,2BAA2B;UAACrF,KAAK,EAAEsE,iBAAiB,CAAC,CAAE;UAAAgB,QAAA,GACjEpB,kBAAkB,CAAChI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyJ,SAAS,EAAEzJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0J,YAAY,CAAC,EAAC,KAAG,EAACpB,iBAAiB,CAACtI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuI,WAAW,CAAC;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3H,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLP,aAAa,CAACjH,GAAG,CAACgB,KAAK,IAAIkG,WAAW,CAAClG,KAAK,CAAC,CAAC;IAAA;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvJ,EAAA,CA5iBIN,yBAAyB;AAAAgK,EAAA,GAAzBhK,yBAAyB;AA8iB/B,eAAeA,yBAAyB;AAAA,IAAAgK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}