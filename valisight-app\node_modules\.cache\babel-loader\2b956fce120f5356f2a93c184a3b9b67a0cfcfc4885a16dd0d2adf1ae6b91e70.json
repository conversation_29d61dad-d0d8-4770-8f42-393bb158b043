{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\CustomizeReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { IconButton, Tooltip, CircularProgress, Box, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button, Select, MenuItem, InputLabel, FormControl, TextField } from '@mui/material';\nimport { Sync as SyncIcon, ArrowBack, Download as DownloadIcon } from '@mui/icons-material';\nimport { Snackbar, Alert } from '@mui/material';\nimport { useSearchParams } from 'react-router-dom';\nimport TableOfContents from './ReportPages/TableOfContents';\nimport ReportSummary from './ReportPages/ReportSummary';\nimport FiscalYearDashboard from './ReportPages/FiscalYear';\nimport ExpenseSummaryDashboard from './ReportPages/ExpenseSummary';\nimport OperationalEfficiencyDashboard from './ReportPages/OperationalEfficiency';\nimport LiquiditySummaryDashboard from './ReportPages/LiquiditySummary';\nimport ProfitLoss13MonthDashboard from './ReportPages/MonthTrailing';\nimport ProfitLossMonthlyDashboard from './ReportPages/Monthly';\nimport ProfitLossYTDDashboard from './ReportPages/YearToDate';\nimport BalanceSheetDashboard from './ReportPages/BalanceSheet';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { downloadPDFFromBase64 } from '../../services/pdf';\nimport { getContentSettings, checkQBOConnectionStatus, getTemplateSettings, updateTemplateSettings, generateReportCalculation, uploadChartsToS3, getGlobalSettings } from '../../services/customizeReportService';\nimport DeepSightCoverPage from './ReportPages/CoverPage';\nimport ApexCharts from 'apexcharts';\nimport { InfoOutlined as InfoIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomizeTemplateWithPreview = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const params = useParams();\n  const companyId = params.id;\n  const reportId = params.reportId;\n\n  // API Data State\n  const [reportData, setReportData] = useState(null);\n  const [isLoadingData, setIsLoadingData] = useState(true);\n  const [dataError, setDataError] = useState(null);\n\n  // Template Settings State\n  const [templateSettings, setTemplateSettings] = useState(null);\n  const [initialSettings, setInitialSettings] = useState(null); // Store initial settings for reset\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\n  const [settingsError, setSettingsError] = useState(null);\n\n  // User State\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isLoadingUser, setIsLoadingUser] = useState(true);\n\n  // Content Settings State (NEW)\n  const [contentSettings, setContentSettings] = useState(null);\n  const [isLoadingContentSettings, setIsLoadingContentSettings] = useState(true);\n  const [contentSettingsError, setContentSettingsError] = useState(null);\n\n  // UI State\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('Deepsight');\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n  const [isSavingSettings, setIsSavingSettings] = useState(false);\n  const [isResettingSettings, setIsResettingSettings] = useState(false);\n\n  // Modal State\n  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);\n  const [showResetConfirmModal, setShowResetConfirmModal] = useState(false);\n\n  //qbo connection status\n  const [qboConnectionStatus, setQboConnectionStatus] = useState(null);\n  const [isCheckingConnection, setIsCheckingConnection] = useState(false);\n\n  // Auto-redirect timer state\n  const [redirectCountdown, setRedirectCountdown] = useState(null);\n  const [redirectTimerRef, setRedirectTimerRef] = useState(null);\n  const navigate = useNavigate();\n\n  //fetching company id for report data\n\n  // Default settings fallback\n  const defaultSettings = {\n    header: {\n      fontStyle: 'Open Sans',\n      fontType: 'Bold',\n      fontSize: 44,\n      color: '#1e7c8c'\n    },\n    heading: {\n      fontStyle: 'Open Sans',\n      fontType: 'Bold',\n      fontSize: 24,\n      color: '#1e7c8c'\n    },\n    h2: {\n      fontStyle: \"Open Sans\",\n      fontType: \"Bold\",\n      fontSize: 24,\n      color: '#333333'\n    },\n    h3: {\n      fontStyle: \"Open Sans\",\n      fontType: \"Bold\",\n      fontSize: 18,\n      color: '#333333'\n    },\n    subHeading: {\n      fontStyle: 'Open Sans',\n      fontType: 'Bold',\n      fontSize: 28,\n      color: '#1e7c8c'\n    },\n    content: {\n      fontStyle: 'Open Sans',\n      fontType: 'Regular',\n      fontSize: 16,\n      color: '#333333'\n    }\n  };\n  const fontStyles = [\"Open Sans\", 'Calibri', 'Arial', 'Times New Roman', 'Georgia', 'Verdana', 'Helvetica'];\n  const fontTypes = ['Regular', 'Bold'];\n\n  // Font size constraints\n  const fontSizeConstraints = {\n    header: {\n      min: 32,\n      max: 48\n    },\n    heading: {\n      min: 20,\n      max: 40\n    },\n    h2: {\n      min: 20,\n      max: 40\n    },\n    h3: {\n      min: 15,\n      max: 28\n    },\n    subHeading: {\n      min: 18,\n      max: 28\n    },\n    content: {\n      min: 9,\n      max: 20\n    }\n  };\n\n  // Fetch content settings from API (NEW)\n  const fetchContentSettings = async () => {\n    try {\n      setIsLoadingContentSettings(true);\n      setContentSettingsError(null);\n      const response = await getContentSettings(companyId, 'DEEPSIGHT');\n      if (response.data && response.data.success) {\n        const settingsData = response.data.data;\n        setContentSettings(settingsData);\n      } else {\n        throw new Error('Failed to fetch content settings');\n      }\n    } catch (error) {\n      console.error('Error fetching content settings:', error);\n      setContentSettingsError(error.message);\n\n      // Set default content settings if API fails\n      const defaultContentSettings = {\n        chartSettings: {\n          incomeSummary: true,\n          netIncome: true,\n          grossProfitMargin: true,\n          netProfitMargin: true,\n          roaAndRoe: true\n        }\n      };\n      setContentSettings(defaultContentSettings);\n      setSuccessMessage('Using default chart settings. Failed to load from server.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingContentSettings(false);\n    }\n  };\n\n  //check qbo connection status\n  const checkQBOConnectionStatusLocal = async () => {\n    try {\n      setIsCheckingConnection(true);\n      const response = await checkQBOConnectionStatus(companyId);\n      if (response.data && response.data.success) {\n        setQboConnectionStatus(response.data.data);\n        return response.data.data;\n      } else {\n        throw new Error('Failed to check QBO connection status');\n      }\n    } catch (error) {\n      console.error('Error checking QBO connection status:', error);\n      // Assume disconnected if API call fails\n      setQboConnectionStatus({\n        connectionStatus: 'DISCONNECTED'\n      });\n      return {\n        connectionStatus: 'DISCONNECTED'\n      };\n    } finally {\n      setIsCheckingConnection(false);\n    }\n  };\n\n  // Fetch template settings from API\n  const fetchTemplateSettings = async () => {\n    try {\n      setIsLoadingSettings(true);\n      setIsLoadingUser(true);\n      setSettingsError(null);\n      const response = await getTemplateSettings();\n      if (response.data && response.data.success) {\n        const settingsData = response.data.data.settings;\n        const userData = response.data.user;\n\n        // Store settings in localStorage\n        localStorage.setItem('templateSettings', JSON.stringify(settingsData));\n\n        // Set both current and initial settings\n        setTemplateSettings(settingsData);\n        setInitialSettings(settingsData);\n\n        // Set user information from the API response\n        if (userData) {\n          setCurrentUser(userData);\n        }\n      } else {\n        throw new Error('Failed to fetch template settings');\n      }\n    } catch (error) {\n      console.error('Error fetching template settings:', error);\n      setSettingsError(error.message);\n\n      // Fallback to localStorage or default settings\n      const savedSettings = localStorage.getItem('templateSettings');\n      const fallbackSettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n      setTemplateSettings(fallbackSettings);\n      setInitialSettings(fallbackSettings);\n      setSuccessMessage('Using cached settings. Failed to load from server.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingSettings(false);\n      setIsLoadingUser(false);\n    }\n  };\n\n  // Load settings from URL params (for PDF generation) or fetch from API\n  const initializeSettings = () => {\n    try {\n      // First check if settings are in URL (for PDF generation)\n      const urlSettings = searchParams.get('templateSettings');\n      if (urlSettings) {\n        const parsedSettings = JSON.parse(urlSettings);\n        setTemplateSettings(parsedSettings);\n        setInitialSettings(parsedSettings);\n        setIsLoadingSettings(false);\n        return;\n      }\n\n      // Otherwise fetch from API\n      fetchTemplateSettings();\n    } catch (error) {\n      console.error('Error initializing settings:', error);\n      setTemplateSettings(defaultSettings);\n      setInitialSettings(defaultSettings);\n      setIsLoadingSettings(false);\n    }\n  };\n\n  // Update the fetchReportData function to use the companyId from params\n  const fetchReportData = async () => {\n    try {\n      setIsLoadingData(true);\n      setDataError(null);\n\n      // Check QBO connection status first\n      const connectionStatus = await checkQBOConnectionStatusLocal();\n\n      // Only proceed if connected\n      if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\n        const response = await generateReportCalculation(companyId, reportId);\n        if (response.data && response.data.success) {\n          setReportData(response.data.data);\n        } else {\n          throw new Error('Failed to fetch report data');\n        }\n      } else {\n        // If not connected, set error state\n        setDataError('Company not connected to QuickBooks');\n        setSuccessMessage('Company is not connected to QuickBooks. Please connect to generate reports.');\n        setShowSuccess(true);\n      }\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setDataError(error.message);\n      setSuccessMessage('Failed to load report data. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingData(false);\n    }\n  };\n  // Generate and download PDF\n  // In CustomizeReport.jsx, update the handleDownloadPDF function\n\n  // Function to add padding around chart images with enhanced styling\n  const addPaddingToImage = (imageDataURI, padding = 40) => {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      img.onload = () => {\n        try {\n          // Create canvas with padding\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d');\n\n          // Set canvas size with padding\n          canvas.width = img.width + padding * 2;\n          canvas.height = img.height + padding * 2;\n\n          // Fill background with white\n          ctx.fillStyle = '#ffffff';\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n          // Add subtle border (optional)\n          ctx.strokeStyle = '#e5e7eb';\n          ctx.lineWidth = 1;\n          ctx.strokeRect(padding - 1, padding - 1, img.width + 2, img.height + 2);\n\n          // Draw the chart image with padding offset\n          ctx.drawImage(img, padding, padding);\n\n          // Convert to data URI with high quality\n          const paddedDataURI = canvas.toDataURL('image/png', 0.95);\n          resolve(paddedDataURI);\n        } catch (error) {\n          reject(error);\n        }\n      };\n      img.onerror = () => {\n        reject(new Error('Failed to load chart image'));\n      };\n      img.src = imageDataURI;\n    });\n  };\n\n  // Chart export configuration\n  const chartExportConfig = {\n    // ExpenseSummary charts\n    roaRoeChart: {\n      name: 'ROA_ROE_Chart',\n      title: 'ROA & ROE Chart'\n    },\n    expensesPieChart: {\n      name: 'Expenses_Pie_Chart',\n      title: 'Expenses Pie Chart'\n    },\n    expensesMonthlyChart: {\n      name: 'Expenses_Top_Accounts_Monthly_Chart',\n      title: 'Expenses: Top Accounts Monthly'\n    },\n    wagesRevenueChart: {\n      name: 'Wages_Revenue_Chart',\n      title: 'Wages vs Revenue Chart'\n    },\n    // FiscalYear charts\n    stackedColumnChart: {\n      name: 'Monthly_Performance_Chart',\n      title: 'Monthly Performance Breakdown'\n    },\n    netIncomeChart: {\n      name: 'Net_Income_Chart',\n      title: 'Net Income Chart'\n    },\n    grossProfitChart: {\n      name: 'Gross_Profit_Margin_Chart',\n      title: 'Gross Profit Margin'\n    },\n    netProfitMarginChart: {\n      name: 'Net_Profit_Margin_Chart',\n      title: 'Net Profit Margin Chart'\n    },\n    // OperationalEfficiency charts\n    salesOutstandingChart: {\n      name: 'Days_Sales_Outstanding_Chart',\n      title: 'Days Sales (A/R) Outstanding'\n    },\n    payablesOutstandingChart: {\n      name: 'Days_Payables_Outstanding_Chart',\n      title: 'Days Payables (AP) Outstanding'\n    },\n    inventoryOutstandingChart: {\n      name: 'Days_Inventory_Outstanding_Chart',\n      title: 'Days Inventory Outstanding'\n    },\n    cashConversionChart: {\n      name: 'Cash_Conversion_Cycle_Chart',\n      title: 'Cash Conversion Cycle'\n    },\n    fixedAssetTurnoverChart: {\n      name: 'Fixed_Asset_Turnover_Chart',\n      title: 'Fixed Asset Turnover Chart'\n    },\n    // LiquiditySummary charts\n    netChangeChart: {\n      name: 'Net_Change_Cash_Chart',\n      title: 'Net Change in Cash Chart'\n    },\n    quickRatioChart: {\n      name: 'Quick_Ratio_Chart',\n      title: 'Quick Ratio Chart'\n    },\n    monthsCashChart: {\n      name: 'Months_Cash_Chart',\n      title: 'Months Cash on Hand Chart'\n    }\n  };\n\n  // Generic function to upload a single chart to S3\n  const uploadSingleChartToS3 = async chartKey => {\n    try {\n      const chart = window[chartKey];\n      if (!chart) {\n        var _chartExportConfig$ch;\n        throw new Error(`${((_chartExportConfig$ch = chartExportConfig[chartKey]) === null || _chartExportConfig$ch === void 0 ? void 0 : _chartExportConfig$ch.title) || chartKey} not found or not rendered.`);\n      }\n\n      // Wait for chart to be stable\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Use ApexCharts built-in export functionality\n      const uri = await chart.dataURI({\n        type: 'png',\n        width: 1200,\n        height: 600,\n        scale: 1,\n        quality: 0.95\n      });\n      if (!uri || !uri.imgURI) {\n        throw new Error('Failed to generate chart image data.');\n      }\n\n      // Add padding to the image\n      const paddedImageDataURI = await addPaddingToImage(uri.imgURI, 40);\n\n      // Prepare chart data for upload\n      const config = chartExportConfig[chartKey];\n      const chartData = {\n        chartData: paddedImageDataURI,\n        chartUniqueName: config.name,\n        chartName: config.title\n      };\n      return {\n        success: true,\n        chartData,\n        title: config.title\n      };\n    } catch (error) {\n      var _chartExportConfig$ch2;\n      console.error(`Chart preparation error for ${chartKey}:`, error);\n      return {\n        success: false,\n        error: error.message,\n        title: ((_chartExportConfig$ch2 = chartExportConfig[chartKey]) === null || _chartExportConfig$ch2 === void 0 ? void 0 : _chartExportConfig$ch2.title) || chartKey\n      };\n    }\n  };\n\n  // Background chart upload function (non-blocking)\n  const uploadChartsInBackground = async () => {\n    try {\n      const chartResults = [];\n      const chartKeys = Object.keys(chartExportConfig);\n\n      // Debug: Log which charts are available\n      chartKeys.forEach(chartKey => {\n        const isAvailable = !!window[chartKey];\n        if (!isAvailable) {\n          console.warn(`Background: Missing chart: ${chartKey} - ${chartExportConfig[chartKey].title}`);\n        }\n      });\n\n      // Prepare all chart data\n      for (const chartKey of chartKeys) {\n        if (window[chartKey]) {\n          const result = await uploadSingleChartToS3(chartKey);\n          chartResults.push(result);\n\n          // Small delay between preparations\n          await new Promise(resolve => setTimeout(resolve, 200));\n        } else {\n          console.warn(`Background: Skipping ${chartKey} - chart not found or not rendered`);\n        }\n      }\n      const successful = chartResults.filter(r => r.success);\n      const failed = chartResults.filter(r => !r.success);\n      if (successful.length > 0) {\n        // Upload charts to S3\n        const chartsToUpload = successful.map(result => result.chartData);\n\n        // Use the actual report ID from URL params, fallback to timestamp if not available\n        const actualReportId = reportId || Date.now();\n        const dateRequested = new Date().toISOString();\n        const uploadResponse = await uploadChartsToS3(chartsToUpload, companyId, actualReportId, dateRequested);\n        if (uploadResponse.data.success) {\n          const uploadResults = uploadResponse.data.data;\n          // // Show subtle notification that charts were uploaded (optional)\n          // setTimeout(() => {\n          //   // setSuccessMessage(`Charts uploaded to S3 successfully (${uploadResults.successful} files)`);\n          //   setShowSuccess(true);\n          // }, 2000); // Show after 2 seconds\n        } else {\n          console.error('Background: Failed to upload charts to S3');\n        }\n      }\n    } catch (chartError) {\n      console.error('Background: Chart upload error:', chartError);\n      // Silently handle errors in background process\n    }\n  };\n\n  // Note: Individual chart export functions removed as charts are now exported with PDF\n\n  const handleDownloadPDF = async () => {\n    try {\n      setIsGeneratingPDF(true);\n      uploadChartsInBackground();\n      setSuccessMessage('Generating PDF...');\n      setShowSuccess(true);\n\n      // Get the scrollable content panel that contains all components\n      const contentPanel = document.querySelector('.flex-1.overflow-y-auto.bg-gray-200');\n      if (!contentPanel) {\n        throw new Error('Could not find report content to generate PDF');\n      }\n\n      // Wait for charts to fully render before capturing content\n      await new Promise(resolve => setTimeout(resolve, 3000));\n\n      // FREEZE CHART ELEMENTS AND LEGENDS BEFORE CLONING\n      const freezeChartsAndLegends = container => {\n        // 1. Freeze Chart Elements\n        const chartContainers = container.querySelectorAll('div[id*=\"apex\"]');\n        chartContainers.forEach((chartContainer, index) => {\n          try {\n            // Freeze all SVG elements within the chart\n            const svgElements = chartContainer.querySelectorAll('svg, svg *');\n            svgElements.forEach(svgEl => {\n              // Get computed styles and apply them inline\n              const computedStyle = window.getComputedStyle(svgEl);\n\n              // Apply critical styles inline to prevent changes\n              if (svgEl.tagName.toLowerCase() === 'svg') {\n                svgEl.style.width = computedStyle.width;\n                svgEl.style.height = computedStyle.height;\n                svgEl.style.overflow = 'visible';\n              }\n\n              // Freeze stroke and fill properties\n              if (computedStyle.stroke && computedStyle.stroke !== 'none') {\n                svgEl.style.stroke = computedStyle.stroke;\n              }\n              if (computedStyle.strokeWidth && computedStyle.strokeWidth !== '0px') {\n                svgEl.style.strokeWidth = computedStyle.strokeWidth;\n              }\n              if (computedStyle.fill && computedStyle.fill !== 'none') {\n                svgEl.style.fill = computedStyle.fill;\n              }\n              if (computedStyle.strokeDasharray && computedStyle.strokeDasharray !== 'none') {\n                svgEl.style.strokeDasharray = computedStyle.strokeDasharray;\n              }\n\n              // Prevent any transformations from changing\n              if (computedStyle.transform && computedStyle.transform !== 'none') {\n                svgEl.style.transform = computedStyle.transform;\n              }\n            });\n\n            // Freeze the chart container dimensions and position\n            const containerRect = chartContainer.getBoundingClientRect();\n            chartContainer.style.width = containerRect.width + 'px';\n            chartContainer.style.height = containerRect.height + 'px';\n            chartContainer.style.position = 'relative';\n            chartContainer.style.overflow = 'visible';\n\n            // Freeze all ApexCharts specific elements\n            const apexElements = chartContainer.querySelectorAll('[class*=\"apexcharts\"]');\n            apexElements.forEach(element => {\n              const computedStyle = window.getComputedStyle(element);\n\n              // Preserve positioning\n              if (computedStyle.position && computedStyle.position !== 'static') {\n                element.style.position = computedStyle.position;\n                element.style.top = computedStyle.top;\n                element.style.left = computedStyle.left;\n                element.style.right = computedStyle.right;\n                element.style.bottom = computedStyle.bottom;\n              }\n\n              // Preserve dimensions\n              element.style.width = computedStyle.width;\n              element.style.height = computedStyle.height;\n\n              // Preserve display and visibility\n              element.style.display = computedStyle.display;\n              element.style.visibility = computedStyle.visibility;\n              element.style.opacity = computedStyle.opacity;\n            });\n\n            // Specifically handle grid lines and axis elements that might be causing extra lines\n            const gridLines = chartContainer.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\n            gridLines.forEach(line => {\n              const computedStyle = window.getComputedStyle(line);\n              line.style.stroke = computedStyle.stroke;\n              line.style.strokeWidth = computedStyle.strokeWidth;\n              line.style.strokeDasharray = computedStyle.strokeDasharray;\n              line.style.opacity = computedStyle.opacity;\n            });\n\n            // Handle axis lines\n            const axisLines = chartContainer.querySelectorAll('.apexcharts-xaxis line, .apexcharts-yaxis line');\n            axisLines.forEach(line => {\n              const computedStyle = window.getComputedStyle(line);\n              line.style.stroke = computedStyle.stroke;\n              line.style.strokeWidth = computedStyle.strokeWidth;\n              line.style.opacity = computedStyle.opacity;\n            });\n\n            // Handle data series paths/bars\n            const seriesElements = chartContainer.querySelectorAll('.apexcharts-series path, .apexcharts-series rect, .apexcharts-series circle');\n            seriesElements.forEach(element => {\n              const computedStyle = window.getComputedStyle(element);\n              if (computedStyle.fill) element.style.fill = computedStyle.fill;\n              if (computedStyle.stroke) element.style.stroke = computedStyle.stroke;\n              if (computedStyle.strokeWidth) element.style.strokeWidth = computedStyle.strokeWidth;\n              if (computedStyle.opacity) element.style.opacity = computedStyle.opacity;\n            });\n          } catch (error) {\n            console.error(`Error freezing chart ${index}:`, error);\n          }\n        });\n\n        // 2. Freeze Legend Elements (your existing legend freezing code)\n        const legends = container.querySelectorAll('.apexcharts-legend');\n        legends.forEach((legend, index) => {\n          try {\n            // Get current position relative to viewport\n            const rect = legend.getBoundingClientRect();\n\n            // Find the closest chart container or parent container\n            let chartContainer = legend.closest('div[id*=\"apex\"]');\n            if (!chartContainer) {\n              // Look for chart section container\n              chartContainer = legend.closest('.bg-white.p-6');\n            }\n            if (!chartContainer) {\n              // Fallback to component container\n              chartContainer = legend.closest('.min-h-screen');\n            }\n            if (chartContainer) {\n              const parentRect = chartContainer.getBoundingClientRect();\n\n              // Calculate position relative to the chart container\n              const relativeTop = rect.top - parentRect.top;\n              const relativeLeft = rect.left - parentRect.left;\n\n              // Store original styles for debugging\n              legend.setAttribute('data-original-position', legend.style.position || '');\n              legend.setAttribute('data-original-top', legend.style.top || '');\n              legend.setAttribute('data-original-left', legend.style.left || '');\n\n              // Set the legend to absolute positioning relative to its chart container\n              legend.style.position = 'absolute';\n              legend.style.top = Math.max(0, relativeTop) + 'px';\n              legend.style.left = relativeLeft + 'px';\n              legend.style.right = 'auto';\n              legend.style.bottom = 'auto';\n              legend.style.transform = 'none';\n              legend.style.zIndex = '10';\n\n              // Ensure the chart container has relative positioning\n              const containerPosition = window.getComputedStyle(chartContainer).position;\n              if (containerPosition === 'static') {\n                chartContainer.style.position = 'relative';\n              }\n\n              // Freeze legend's internal styling\n              const legendElements = legend.querySelectorAll('*');\n              legendElements.forEach(el => {\n                const computedStyle = window.getComputedStyle(el);\n                if (computedStyle.color) el.style.color = computedStyle.color;\n                if (computedStyle.fontSize) el.style.fontSize = computedStyle.fontSize;\n                if (computedStyle.fontFamily) el.style.fontFamily = computedStyle.fontFamily;\n                if (computedStyle.fontWeight) el.style.fontWeight = computedStyle.fontWeight;\n              });\n            } else {\n              console.warn(`Could not find chart container for legend ${index}`);\n\n              // Fallback: just prevent the legend from moving by setting fixed position\n              legend.style.position = 'relative';\n              legend.style.top = '20px';\n              legend.style.left = 'auto';\n              legend.style.right = 'auto';\n              legend.style.transform = 'none';\n              legend.style.margin = '20px auto';\n              legend.style.display = 'flex';\n              legend.style.justifyContent = 'center';\n            }\n          } catch (error) {\n            console.error(`Error freezing legend ${index}:`, error);\n\n            // Emergency fallback - just make it static\n            legend.style.position = 'static';\n            legend.style.margin = '20px auto';\n            legend.style.display = 'flex';\n            legend.style.justifyContent = 'center';\n          }\n        });\n      };\n\n      // Apply the freezing function\n      freezeChartsAndLegends(contentPanel);\n\n      // Wait a bit more for positions to settle after freezing\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Now clone the content with frozen chart and legend positions\n      const clonedContent = contentPanel.cloneNode(true);\n\n      // Additional safety check: ensure no extra lines are added during cloning\n      const clonedCharts = clonedContent.querySelectorAll('div[id*=\"apex\"]');\n      clonedCharts.forEach((chart, index) => {\n        // Remove any duplicate or extra grid lines that might have been created\n        const gridLines = chart.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\n        const seenLines = new Set();\n        gridLines.forEach(line => {\n          const lineKey = `${line.getAttribute('x1')}-${line.getAttribute('y1')}-${line.getAttribute('x2')}-${line.getAttribute('y2')}`;\n          if (seenLines.has(lineKey)) {\n            // Duplicate line, remove it\n            line.remove();\n          } else {\n            seenLines.add(lineKey);\n          }\n        });\n\n        // Ensure chart maintains its frozen dimensions\n        chart.style.minHeight = 'auto';\n        chart.style.maxHeight = 'none';\n      });\n\n      // Additional safety check: fix any legends that might still be mispositioned in cloned content\n      const clonedLegends = clonedContent.querySelectorAll('.apexcharts-legend');\n      clonedLegends.forEach((legend, index) => {\n        // If legend still has a very high top value, reset it\n        const topValue = parseFloat(legend.style.top) || 0;\n        if (topValue > 500) {\n          legend.style.top = '20px';\n          legend.style.position = 'relative';\n          legend.style.margin = '20px auto 10px auto';\n          legend.style.display = 'flex';\n          legend.style.justifyContent = 'center';\n        }\n      });\n\n      // Find all components and modify them for proper header repetition\n      const components = clonedContent.querySelectorAll('.min-h-screen');\n      components.forEach(component => {\n        const componentHeader = component.querySelector('.component-header');\n        const reportHeader = component.querySelector('.report-header');\n        const metricGrid = component.querySelector(\".metrics-flex\");\n        let header = componentHeader || reportHeader;\n\n        // Check if this is the MonthTrailing component by looking for the unique max-w-8xl class\n        // Also check for the specific text content as a fallback, but only if it has the right structure\n        const hasMaxW8xl = component.querySelector('.max-w-8xl') !== null;\n        const hasTrailingText = component.textContent.includes('13 Month Trailing');\n        const hasProfitLossTable = component.querySelector('.profit-loss-table') !== null;\n        const isMonthTrailing = hasMaxW8xl || hasTrailingText && hasProfitLossTable;\n\n        // Check if this component should have repeating headers\n        // MonthTrailing (max-w-8xl), Monthly (max-w-6xl + profit-loss-section), YearToDate (max-w-6xl + profit-loss-section), BalanceSheet\n        const shouldHaveRepeatingHeaders = isMonthTrailing || component.querySelector('.max-w-6xl') && component.classList.contains('profit-loss-section') || component.textContent.includes('Balance Sheet');\n        if (header) {\n          if (!header.classList.contains('component-header')) {\n            header.classList.add('component-header');\n          }\n\n          // Add special class for MonthTrailing component\n          if (isMonthTrailing) {\n            component.classList.add('month-trailing-component');\n            header.classList.add('month-trailing-header');\n\n            // Add data attribute for more reliable targeting\n            component.setAttribute('data-component', 'month-trailing');\n\n            // Also add inline styles to ensure page orientation is applied\n            component.style.page = 'month-trailing';\n            component.style.pageBreakBefore = 'always';\n\n            // Try setting the page size directly on the component\n            component.style.setProperty('page', 'month-trailing', 'important');\n          }\n\n          // Add class for components that should have repeating headers\n          if (shouldHaveRepeatingHeaders) {\n            header.classList.add('repeating-header');\n\n            // Ensure the header structure is correct for repetition\n            header.style.display = 'table-header-group';\n            header.style.pageBreakInside = 'avoid';\n            header.style.pageBreakAfter = 'avoid';\n          }\n          const allChildren = Array.from(component.children);\n          let nonHeaderChildren;\n          if (componentHeader) {\n            nonHeaderChildren = allChildren.filter(child => !child.classList.contains('component-header'));\n          } else if (reportHeader) {\n            nonHeaderChildren = allChildren.filter(child => !child.classList.contains('report-header'));\n          }\n          if (nonHeaderChildren && nonHeaderChildren.length > 0) {\n            const contentGroup = document.createElement('div');\n            contentGroup.className = 'component-content';\n            nonHeaderChildren.forEach(child => {\n              contentGroup.appendChild(child);\n            });\n            component.appendChild(contentGroup);\n          }\n          component.style.display = 'table';\n          component.style.width = '100%';\n        }\n\n        // Ensure table headers repeat for components with repeating headers\n        if (shouldHaveRepeatingHeaders) {\n          const tables = component.querySelectorAll('table');\n          tables.forEach(table => {\n            const thead = table.querySelector('thead');\n            if (thead) {\n              thead.style.display = 'table-header-group';\n              thead.style.pageBreakInside = 'avoid';\n\n              // Add specific classes for better control\n              thead.classList.add('repeating-table-header');\n\n              // Ensure table structure is correct for header repetition\n              table.style.borderCollapse = 'collapse';\n            }\n          });\n        }\n      });\n\n      // Create HTML with minimal CSS changes\n      const htmlContent = `\n      <!DOCTYPE html>\n      <html lang=\"en\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Custom Report</title>\n        <script src=\"https://cdn.tailwindcss.com\"></script>\n        <link href=\"https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap\" rel=\"stylesheet\">\n        <style>\n          @page {\n            size: A4 portrait;\n            margin-top: 0in;\n          }\n\n          /* Special page settings for different components */\n          @page month-trailing {\n            size: letter landscape;\n            margin-top : 0.5in;\n          }\n\n          @page monthly-report {\n            size: A4 portrait;\n            margin-top : 0.5in;\n          }\n\n          @page year-to-date-report {\n            size: A4 portrait;\n            margin-top : 0.5in;\n          }\n\n          @page balance-sheet-report {\n            size: A4 portrait;\n            margin-top : 0.5in;\n          }\n\n          /* Alternative approach - try different page rule syntax */\n          @page {\n            size: A4 portrait;\n          }\n\n          /* Specific page rule for landscape components */\n          .month-trailing-component {\n            page: month-trailing;\n          }\n\n          @media print {\n            .month-trailing-component {\n              page: month-trailing;\n            }\n\n            @page month-trailing {\n              size: letter landscape;\n              margin-top : 0.5in;\n            }\n          }\n\n          body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            line-height: 1.6;\n            color: #374151;\n            background: white;\n          }\n\n          .page-break {\n            page-break-before: always;\n          }\n\n          .no-break {\n            page-break-inside: avoid;\n          }\n\n          /* MonthTrailing component specific styles */\n          .month-trailing-component {\n            page: month-trailing;\n            page-break-before: always;\n          }\n\n          /* Force landscape orientation for month trailing components */\n          .month-trailing-component,\n          .month-trailing-component * {\n            page: month-trailing !important;\n          }\n\n          /* Alternative CSS approach for month trailing */\n          .max-w-8xl {\n            page: month-trailing;\n          }\n\n          /* Data attribute targeting */\n          [data-component=\"month-trailing\"] {\n            page: month-trailing !important;\n            page-break-before: always !important;\n          }\n\n          /* Multiple targeting approaches */\n          .month-trailing-component,\n          [data-component=\"month-trailing\"],\n          .max-w-8xl {\n            page: month-trailing !important;\n          }\n\n          /* Fallback approach using CSS transforms if @page doesn't work */\n          @media print {\n            .month-trailing-component {\n              page: month-trailing;\n              transform-origin: top left;\n              width: 11in;  /* US Letter width */\n              height: 8.5in; /* US Letter height in landscape */\n            }\n          }\n\n          /* Repeating headers for specific components */\n          .repeating-header {\n            display: table-header-group !important;\n            page-break-inside: avoid;\n            page-break-after: avoid;\n          }\n\n          .month-trailing-header {\n            display: table-header-group !important;\n            page-break-inside: avoid;\n            page-break-after: avoid;\n          }\n\n          /* Table headers that should repeat */\n          .table-header-group {\n            display: table-header-group !important;\n          }\n\n          .profit-loss-table thead {\n            display: table-header-group !important;\n          }\n\n          .repeating-table-header {\n            display: table-header-group !important;\n            page-break-inside: avoid !important;\n            page-break-after: avoid !important;\n          }\n\n          /* Ensure all table headers in components with repeating headers repeat */\n          .repeating-header ~ * table thead,\n          .month-trailing-component table thead,\n          .profit-loss-section table thead {\n            display: table-header-group !important;\n            page-break-inside: avoid !important;\n          }\n\n          /* Specific styles for different component types */\n          .profit-loss-section .report-header,\n          .balance-sheet-section .report-header {\n            display: table-header-group !important;\n            page-break-inside: avoid !important;\n            page-break-after: avoid !important;\n            margin-top : 0.5in;\n          }\n\n          /* Minimal styles - rely on frozen positions */\n          .border-b-4.border-blue-900 {\n            border-bottom: 4px solid #1e3a8a !important;\n            page-break-inside: avoid;\n            display: table-header-group;\n          }\n\n          .report-header {\n            page-break-inside: avoid;\n          }\n\n          .component-header {\n            display: flex !important;\n            justify-content: space-between !important;\n            align-items: center !important;\n            page-break-inside: avoid;\n          }\n\n          .metrics-flex{\n            display: flex !important;\n            justify-content: space-between !important;\n            align-items: center !important;\n          }\n\n          .metrics-flex > * {\n            flex: 1 1 0% !important;\n            text-align: center;\n          }\n\n          .component-content {\n            display: table-row-group;\n          }\n\n          .min-h-screen {\n            display: table !important;\n            width: 100% !important;\n            min-height: auto !important;\n            page-break-inside: avoid;\n          }\n\n          // .min-h-screen:not(:second-child) {\n          //   page-break-before: always;\n          // }\n\n          /* Trust the frozen chart and legend positions */\n          div[id*=\"apex\"] {\n            page-break-inside: avoid;\n          }\n\n          .apexcharts-legend {\n            page-break-inside: avoid;\n          }\n\n          .bg-gray-200 {\n            background-color: #ffffff !important;\n          }\n\n          .bg-white {\n            background-color: #ffffff !important;\n          }\n\n          .overflow-y-auto {\n            overflow: visible !important;\n          }\n\n          /* Component-specific page assignments */\n          .monthly-component {\n            page: monthly-report;\n          }\n\n          .year-to-date-component {\n            page: year-to-date-report;\n          }\n\n          .balance-sheet-component {\n            page: balance-sheet-report;\n          }\n\n          @media print {\n            .monthly-component {\n              page: monthly-report;\n            }\n            \n            .year-to-date-component {\n              page: year-to-date-report;\n            }\n            \n            .balance-sheet-component {\n              page: balance-sheet-report;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"report-container\">\n          ${clonedContent.innerHTML}\n        </div>\n      </body>\n      </html>\n    `;\n\n      // console.log(\"Html content prepared for PDF generation with frozen charts and legends\", htmlContent);\n\n      // Generate PDF using the HTML content\n      const {\n        generatePDFFromHTML\n      } = await import('../../services/pdf');\n      const response = await generatePDFFromHTML(htmlContent, {\n        format: 'A4',\n        orientation: 'portrait',\n        printBackground: true,\n        margin: {\n          top: '0in',\n          right: '0in',\n          bottom: '0.5in',\n          left: '0in'\n        },\n        preferCSSPageSize: true // Enable CSS @page rules for mixed orientations\n      });\n      if (response.success && response.data.pdf) {\n        const filename = `Custom_Report_${new Date().toISOString().split('T')[0]}.pdf`;\n        const downloadSuccess = downloadPDFFromBase64(response.data.pdf, filename);\n        if (downloadSuccess) {\n          setSuccessMessage('PDF downloaded successfully!');\n          setShowSuccess(true);\n        } else {\n          throw new Error('Failed to download PDF');\n        }\n      } else {\n        throw new Error(response.message || 'Failed to generate PDF');\n      }\n    } catch (error) {\n      console.error('PDF Generation Error:', error);\n      setSuccessMessage('Failed to generate PDF. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    // Ensure ApexCharts is available globally for export functionality\n    if (!window.ApexCharts) {\n      window.ApexCharts = ApexCharts;\n    }\n    initializeSettings();\n    initializeDataWithConnectionCheck();\n    fetchContentSettings(); // NEW: Fetch content settings\n  }, [companyId, reportId]); // Add companyId and reportId as dependencies\n\n  // Auto-redirect timer effect for disconnected state\n  useEffect(() => {\n    // Start timer when showing disconnected state (dataError exists and not connected)\n    if (dataError && qboConnectionStatus && qboConnectionStatus.connectionStatus !== 'CONNECTED' && !isCheckingConnection) {\n      startRedirectTimer();\n    } else {\n      // Clear timer when not in disconnected state\n      clearRedirectTimer();\n    }\n\n    // Cleanup timer on unmount\n    return () => {\n      clearRedirectTimer();\n    };\n  }, [dataError, qboConnectionStatus, isCheckingConnection]);\n\n  // New function to check connection status first, then fetch data\n  const initializeDataWithConnectionCheck = async () => {\n    try {\n      // First check QBO connection status\n      const connectionStatus = await checkQBOConnectionStatusLocal();\n\n      // Only fetch report data if connected\n      if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\n        await fetchReportData();\n      } else {\n        // If disconnected, set appropriate state\n        setIsLoadingData(false);\n        setDataError('Company not connected to QuickBooks');\n      }\n    } catch (error) {\n      console.error('Error during initialization:', error);\n      setIsLoadingData(false);\n      setDataError('Failed to initialize report');\n    }\n  };\n  const handleBackToDashboard = () => {\n    navigate(`/company/${companyId}`, {\n      state: {\n        activeTab: 'reports'\n      }\n    });\n  };\n  const handleSettingChange = (section, property, value) => {\n    // Apply font size constraints\n    if (property === 'fontSize') {\n      const constraints = fontSizeConstraints[section];\n      if (constraints) {\n        value = Math.max(constraints.min, Math.min(constraints.max, value));\n      }\n    }\n    setTemplateSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [property]: value\n      }\n    }));\n  };\n  const handleCloseSuccess = () => {\n    setShowSuccess(false);\n  };\n\n  // Save settings to API\n  const saveSettingsToAPI = async () => {\n    try {\n      setIsSavingSettings(true); // Keep this for save button\n\n      const response = await updateTemplateSettings(templateSettings);\n      if (response.data && response.data.success) {\n        localStorage.setItem('templateSettings', JSON.stringify(templateSettings));\n        setInitialSettings(templateSettings);\n        setSuccessMessage('Settings saved successfully and applied!');\n        setShowSuccess(true);\n      } else {\n        throw new Error('Failed to save template settings');\n      }\n    } catch (error) {\n      console.error('Error saving template settings:', error);\n      setSuccessMessage('Failed to save settings. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsSavingSettings(false); // Keep this for save button\n    }\n  };\n\n  // Button handlers\n  const handleSave = () => {\n    setShowSaveConfirmModal(true);\n  };\n  const handleConfirmSave = async () => {\n    setShowSaveConfirmModal(false);\n    await saveSettingsToAPI();\n  };\n  const handleCancelSave = () => {\n    setShowSaveConfirmModal(false);\n  };\n  const handleResetToDefault = () => {\n    setShowResetConfirmModal(true);\n  };\n  const handleRedirectToConnection = () => {\n    // Clear any existing timer when manually redirecting\n    clearRedirectTimer();\n    navigate(`/company/${companyId}`);\n  };\n\n  // Auto-redirect timer functions\n  const startRedirectTimer = () => {\n    // Clear any existing timer first\n    clearRedirectTimer();\n\n    // Set initial countdown\n    setRedirectCountdown(10);\n\n    // Create interval that decrements countdown every second\n    const timerId = setInterval(() => {\n      setRedirectCountdown(prev => {\n        if (prev <= 1) {\n          // Time's up, redirect and clear timer\n          clearInterval(timerId);\n          setRedirectTimerRef(null);\n          navigate(`/company/${companyId}`);\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    // Store timer reference for cleanup\n    setRedirectTimerRef(timerId);\n  };\n  const clearRedirectTimer = () => {\n    if (redirectTimerRef) {\n      clearInterval(redirectTimerRef);\n      setRedirectTimerRef(null);\n    }\n    setRedirectCountdown(null);\n  };\n  const handleConfirmReset = async () => {\n    try {\n      setIsResettingSettings(true); // Use new state for reset button\n\n      let resetSettings = defaultSettings; // Default fallback\n      let resetMessage = 'Settings reset to default values and saved successfully!';\n\n      // Determine reset behavior based on user role\n      if (currentUser) {\n        if (currentUser.isAdmin) {\n          // Admin users: Reset to hardcoded default settings\n          resetSettings = defaultSettings;\n          resetMessage = 'Settings reset to system default values and saved successfully!';\n        } else {\n          // Normal users: Reset to global settings from database\n          try {\n            const globalResponse = await getGlobalSettings('DEEPSIGHT');\n            if (globalResponse.data && globalResponse.data.success) {\n              resetSettings = globalResponse.data.data.settings;\n              resetMessage = 'Settings reset to global default values and saved successfully!';\n            } else {\n              console.warn('Failed to fetch global settings, using hardcoded defaults');\n            }\n          } catch (globalError) {\n            console.error('Error fetching global settings:', globalError);\n            console.warn('Using hardcoded defaults as fallback');\n          }\n        }\n      }\n\n      // Apply the reset settings\n      setTemplateSettings(resetSettings);\n\n      // Save the reset settings to the server\n      const response = await updateTemplateSettings(resetSettings);\n      if (response.data && response.data.success) {\n        localStorage.setItem('templateSettings', JSON.stringify(resetSettings));\n        setInitialSettings(resetSettings);\n        setSuccessMessage(resetMessage);\n        setShowSuccess(true);\n      } else {\n        throw new Error('Failed to save reset template settings');\n      }\n    } catch (error) {\n      console.error('Error resetting and saving settings:', error);\n      // Fallback to default settings if everything fails\n      setTemplateSettings(defaultSettings);\n      setInitialSettings(defaultSettings);\n      localStorage.setItem('templateSettings', JSON.stringify(defaultSettings));\n      setSuccessMessage('Settings reset to default but failed to save to server. Please try saving manually.');\n      setShowSuccess(true);\n    } finally {\n      setIsResettingSettings(false); // Use new state for reset button\n      setShowResetConfirmModal(false);\n    }\n  };\n  const handleCancelReset = () => {\n    setShowResetConfirmModal(false);\n  };\n  const handleResync = async () => {\n    try {\n      // Check connection status first\n      const connectionStatus = await checkQBOConnectionStatusLocal();\n      if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\n        await fetchReportData();\n        fetchContentSettings(); // Also resync content settings\n        setSuccessMessage('Data resynced successfully!');\n      } else {\n        setSuccessMessage('Cannot resync: Company not connected to QuickBooks.');\n      }\n      setShowSuccess(true);\n    } catch (error) {\n      console.error('Error during resync:', error);\n      setSuccessMessage('Failed to resync data. Please try again.');\n      setShowSuccess(true);\n    }\n  };\n\n  // Convert font type to CSS font-weight\n  const getFontWeight = fontType => {\n    const weights = {\n      'Regular': '400',\n      'Bold': '700'\n    };\n    return weights[fontType] || '400';\n  };\n  const getHeaderStyle = () => {\n    var _templateSettings$hea, _templateSettings$hea2, _templateSettings$hea3, _templateSettings$hea4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea = templateSettings.header) === null || _templateSettings$hea === void 0 ? void 0 : _templateSettings$hea.fontStyle) || 'Open Sans',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea2 = templateSettings.header) === null || _templateSettings$hea2 === void 0 ? void 0 : _templateSettings$hea2.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea3 = templateSettings.header) === null || _templateSettings$hea3 === void 0 ? void 0 : _templateSettings$hea3.fontSize) || 44}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea4 = templateSettings.header) === null || _templateSettings$hea4 === void 0 ? void 0 : _templateSettings$hea4.color) || '#1e7c8c',\n      borderRadius: '8px 8px 0 0',\n      margin: '0'\n    };\n  };\n  const getHeadingStyle = () => {\n    var _templateSettings$hea5, _templateSettings$hea6, _templateSettings$hea7, _templateSettings$hea8;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea5 = templateSettings.heading) === null || _templateSettings$hea5 === void 0 ? void 0 : _templateSettings$hea5.fontStyle) || 'Open Sans',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea6 = templateSettings.heading) === null || _templateSettings$hea6 === void 0 ? void 0 : _templateSettings$hea6.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea7 = templateSettings.heading) === null || _templateSettings$hea7 === void 0 ? void 0 : _templateSettings$hea7.fontSize) || 36}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea8 = templateSettings.heading) === null || _templateSettings$hea8 === void 0 ? void 0 : _templateSettings$hea8.color) || '#1e7c8c'\n    };\n  };\n  const getH2Style = () => {\n    var _templateSettings$h, _templateSettings$h2, _templateSettings$h3, _templateSettings$h4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h = templateSettings.h2) === null || _templateSettings$h === void 0 ? void 0 : _templateSettings$h.fontStyle) || 'Open Sans',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h2 = templateSettings.h2) === null || _templateSettings$h2 === void 0 ? void 0 : _templateSettings$h2.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h3 = templateSettings.h2) === null || _templateSettings$h3 === void 0 ? void 0 : _templateSettings$h3.fontSize) || 24}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h4 = templateSettings.h2) === null || _templateSettings$h4 === void 0 ? void 0 : _templateSettings$h4.color) || '#333333'\n    };\n  };\n  const getH3Style = () => {\n    var _templateSettings$h5, _templateSettings$h6, _templateSettings$h7, _templateSettings$h8;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h5 = templateSettings.h3) === null || _templateSettings$h5 === void 0 ? void 0 : _templateSettings$h5.fontStyle) || 'Open Sans',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h6 = templateSettings.h3) === null || _templateSettings$h6 === void 0 ? void 0 : _templateSettings$h6.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h7 = templateSettings.h3) === null || _templateSettings$h7 === void 0 ? void 0 : _templateSettings$h7.fontSize) || 18}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$h8 = templateSettings.h3) === null || _templateSettings$h8 === void 0 ? void 0 : _templateSettings$h8.color) || '#333333'\n    };\n  };\n  const getContentStyle = () => {\n    var _templateSettings$con, _templateSettings$con2, _templateSettings$con3, _templateSettings$con4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con = templateSettings.content) === null || _templateSettings$con === void 0 ? void 0 : _templateSettings$con.fontStyle) || 'Open Sans',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con2 = templateSettings.content) === null || _templateSettings$con2 === void 0 ? void 0 : _templateSettings$con2.fontType) || 'Regular'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con3 = templateSettings.content) === null || _templateSettings$con3 === void 0 ? void 0 : _templateSettings$con3.fontSize) || 15}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con4 = templateSettings.content) === null || _templateSettings$con4 === void 0 ? void 0 : _templateSettings$con4.color) || '#333333',\n      lineHeight: '1.6',\n      margin: '0'\n    };\n  };\n  const getSubHeadingStyle = () => {\n    var _templateSettings$sub, _templateSettings$sub2, _templateSettings$sub3, _templateSettings$sub4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub = templateSettings.subHeading) === null || _templateSettings$sub === void 0 ? void 0 : _templateSettings$sub.fontStyle) || 'Helvetica',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub2 = templateSettings.subHeading) === null || _templateSettings$sub2 === void 0 ? void 0 : _templateSettings$sub2.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub3 = templateSettings.subHeading) === null || _templateSettings$sub3 === void 0 ? void 0 : _templateSettings$sub3.fontSize) || 22}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub4 = templateSettings.subHeading) === null || _templateSettings$sub4 === void 0 ? void 0 : _templateSettings$sub4.color) || '#1e7c8c',\n      padding: '0'\n    };\n  };\n\n  // Show loading state while fetching initial settings, content settings, or user info\n  if (isLoadingSettings || isLoadingContentSettings || isLoadingUser || !templateSettings) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 text-lg text-gray-600\",\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1423,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1422,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show loading state while checking connection\n  if (isCheckingConnection && qboConnectionStatus === null) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 text-lg text-gray-600\",\n          children: \"Checking QuickBooks connection...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1435,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1434,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if company is connected to QBO\n  const isConnected = qboConnectionStatus && qboConnectionStatus.connectionStatus === 'CONNECTED';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showSuccess,\n      autoHideDuration: 4000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: dataError || settingsError || contentSettingsError ? \"error\" : \"success\",\n        variant: \"filled\",\n        sx: {\n          backgroundColor: dataError || settingsError || contentSettingsError ? '#d32f2f' : '#1976d2',\n          '& .MuiAlert-icon': {\n            color: 'white'\n          }\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1455,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1449,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showSaveConfirmModal,\n      onClose: handleCancelSave,\n      \"aria-labelledby\": \"save-dialog-title\",\n      \"aria-describedby\": \"save-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"save-dialog-title\",\n        children: \"Confirm Save Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"save-dialog-description\",\n          children: \"Are you sure you want to save these template settings? These settings will be applied to all future reports.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1481,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelSave,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmSave,\n          color: \"primary\",\n          variant: \"contained\",\n          disabled: isSavingSettings,\n          children: isSavingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16,\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1497,\n              columnNumber: 17\n            }, this), \"Saving...\"]\n          }, void 0, true) : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1485,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1471,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showResetConfirmModal,\n      onClose: handleCancelReset,\n      \"aria-labelledby\": \"reset-dialog-title\",\n      \"aria-describedby\": \"reset-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"reset-dialog-title\",\n        children: \"Confirm Reset Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"reset-dialog-description\",\n          children: \"Are you sure you want to reset the template settings? Any unsaved changes will be lost.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1518,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1517,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelReset,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmReset,\n          color: \"primary\",\n          variant: \"contained\",\n          disabled: isResettingSettings // Use new state instead of isSavingSettings\n          ,\n          children: isResettingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16,\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 17\n            }, this), \"Resetting...\"]\n          }, void 0, true) : 'Reset Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1522,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1508,\n      columnNumber: 7\n    }, this), isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-96 flex-shrink-0 h-screen flex flex-col fixed left-0 top-0 z-10 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-[14px] border-b bg-gray-50 flex items-center shadow\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Back to Reports\",\n          placement: \"bottom\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleBackToDashboard,\n            sx: {\n              color: 'rgb(75, 85, 99)',\n              padding: '6px',\n              marginRight: '12px',\n              '&:hover': {\n                backgroundColor: 'rgba(75, 85, 99, 0.1)'\n              },\n              '&:focus': {\n                outline: 'none'\n              },\n              transition: 'all 0.2s'\n            },\n            children: /*#__PURE__*/_jsxDEV(ArrowBack, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1564,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1549,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1548,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1547,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 space-y-6 overflow-y-auto flex-1 shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900 font-roboto\",\n          children: \"Customize Template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1572,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"Header\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Customize the main report header \\u2014 typically used for your company name, report title, or client details. Adjust font, color, and size to match your branding.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1582,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1577,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.header.fontStyle,\n                onChange: e => handleSettingChange('header', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1623,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1601,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1590,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.header.fontType,\n                  onChange: e => handleSettingChange('header', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1663,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1642,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1641,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.header.min, \"-\", fontSizeConstraints.header.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1670,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.header.min,\n                  max: fontSizeConstraints.header.max\n                },\n                value: templateSettings.header.fontSize,\n                onChange: e => handleSettingChange('header', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1680,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1669,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.header.color,\n                onChange: e => handleSettingChange('header', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1719,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1708,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1629,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1574,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"Heading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1750,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1756,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1751,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1765,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.heading.fontStyle,\n                onChange: e => handleSettingChange('heading', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1797,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1776,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1775,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1764,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1805,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.heading.fontType,\n                  onChange: e => handleSettingChange('heading', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1837,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1816,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1815,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.heading.min, \"-\", fontSizeConstraints.heading.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.heading.min,\n                  max: fontSizeConstraints.heading.max\n                },\n                value: templateSettings.heading.fontSize,\n                onChange: e => handleSettingChange('heading', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1854,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1843,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1883,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.heading.color,\n                onChange: e => handleSettingChange('heading', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1893,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1882,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1803,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1748,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"H2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1924,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1930,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1925,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1923,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1939,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.h2.fontStyle,\n                onChange: e => handleSettingChange('h2', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1971,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1950,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1949,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1938,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1979,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.h2.fontType,\n                  onChange: e => handleSettingChange('h2', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2011,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1990,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1978,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.h2.min, \"-\", fontSizeConstraints.h2.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2018,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.h2.min,\n                  max: fontSizeConstraints.h2.max\n                },\n                value: templateSettings.h2.fontSize,\n                onChange: e => handleSettingChange('h2', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2028,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2017,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2057,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.h2.color,\n                onChange: e => handleSettingChange('h2', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2067,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2056,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1977,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1922,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"H3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2098,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2104,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2099,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2097,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.h3.fontStyle,\n                onChange: e => handleSettingChange('h3', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2145,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.h3.fontType,\n                  onChange: e => handleSettingChange('h3', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2185,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2164,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.h3.min, \"-\", fontSizeConstraints.h3.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.h3.min,\n                  max: fontSizeConstraints.h3.max\n                },\n                value: templateSettings.h3.fontSize,\n                onChange: e => handleSettingChange('h3', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.h3.color,\n                onChange: e => handleSettingChange('h3', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2096,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"Sub-Heading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Style sub-headings that appear under main sections, for example 'Q1 Performance' under Income Summary. Use this to make detailed sections stand out.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.subHeading.fontStyle,\n                onChange: e => handleSettingChange('subHeading', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2319,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.subHeading.fontType,\n                  onChange: e => handleSettingChange('subHeading', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2359,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2338,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.subHeading.min, \"-\", fontSizeConstraints.subHeading.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2366,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.subHeading.min,\n                  max: fontSizeConstraints.subHeading.max\n                },\n                value: templateSettings.subHeading.fontSize,\n                onChange: e => handleSettingChange('subHeading', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2376,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.subHeading.color,\n                onChange: e => handleSettingChange('subHeading', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 font-roboto\",\n              children: \"Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Format the main body text of your report, including descriptions, analysis, and explanatory content. This affects paragraph text and detailed information throughout the report.\",\n              placement: \"right\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                fontSize: \"small\",\n                className: \"text-gray-500 hover:text-gray-700\",\n                sx: {\n                  fontSize: '17px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                color: 'rgb(75, 85, 99)',\n                marginBottom: '4px'\n              },\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: templateSettings.content.fontStyle,\n                onChange: e => handleSettingChange('content', 'fontStyle', e.target.value),\n                sx: {\n                  '& .MuiSelect-select': {\n                    fontSize: '0.875rem',\n                    padding: '8px 12px',\n                    fontWeight: 400\n                  },\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: 'rgb(209, 213, 219)'\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3b82f6'\n                  }\n                },\n                children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: font,\n                  children: font\n                }, font, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2494,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2473,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  value: templateSettings.content.fontType,\n                  onChange: e => handleSettingChange('content', 'fontType', e.target.value),\n                  sx: {\n                    '& .MuiSelect-select': {\n                      fontSize: '0.875rem',\n                      padding: '8.7px 8px',\n                      fontWeight: 400\n                    },\n                    '& .MuiOutlinedInput-notchedOutline': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2534,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2513,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2512,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: [\"Font Size (\", fontSizeConstraints.content.min, \"-\", fontSizeConstraints.content.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                inputProps: {\n                  min: fontSizeConstraints.content.min,\n                  max: fontSizeConstraints.content.max\n                },\n                value: templateSettings.content.fontSize,\n                onChange: e => handleSettingChange('content', 'fontSize', parseInt(e.target.value)),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    fontSize: '0.875rem',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '10px 8px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2551,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                sx: {\n                  fontSize: '0.75rem',\n                  fontWeight: 500,\n                  color: 'rgb(75, 85, 99)',\n                  marginBottom: '4px'\n                },\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2580,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"color\",\n                value: templateSettings.content.color,\n                onChange: e => handleSettingChange('content', 'color', e.target.value),\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    height: '40px',\n                    '& fieldset': {\n                      borderColor: 'rgb(209, 213, 219)'\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#3b82f6'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#3b82f6'\n                    }\n                  },\n                  '& .MuiOutlinedInput-input': {\n                    padding: '0',\n                    height: '36px',\n                    cursor: 'pointer'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-7 border-t border-gray-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleResetToDefault,\n              disabled: isResettingSettings // Use new state for reset button\n              ,\n              className: \"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-neutral-800\",\n              children: isResettingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 14,\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2628,\n                  columnNumber: 21\n                }, this), \"RESETTING...\"]\n              }, void 0, true) : 'RESET'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              disabled: isSavingSettings // Keep this for save button\n              ,\n              className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-neutral-800\",\n              children: isSavingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 14,\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2643,\n                  columnNumber: 21\n                }, this), \"SAVING...\"]\n              }, void 0, true) : 'SAVE'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2636,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2620,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2619,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1570,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1546,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col flex-1 h-screen\",\n      style: {\n        marginLeft: isConnected ? '384px' : '0'\n      },\n      children: [isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-3 flex items-center bg-gray-50 justify-end fixed top-0 z-5 shadow\",\n        style: {\n          left: isConnected ? '384px' : '0',\n          right: '0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-3\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: isLoadingData ? \"Loading data...\" : \"Resync data\",\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleResync,\n                  disabled: isLoadingData,\n                  sx: {\n                    color: isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\n                    '&:hover': {\n                      backgroundColor: 'rgba(156, 163, 175, 0.1)'\n                    },\n                    '&:focus': {\n                      outline: 'none'\n                    },\n                    '&:disabled': {\n                      color: 'rgba(156, 163, 175, 0.6)',\n                      cursor: 'not-allowed'\n                    },\n                    transition: 'all 0.2s',\n                    padding: '8px'\n                  },\n                  children: isLoadingData ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 16,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2687,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(SyncIcon, {\n                    fontSize: \"medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2689,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2667,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2666,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2665,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: isGeneratingPDF ? \"Generating PDF...\" : \"Download PDF (Charts upload in background)\",\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleDownloadPDF,\n                  disabled: isGeneratingPDF || isLoadingData,\n                  sx: {\n                    color: isGeneratingPDF || isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\n                    '&:hover': {\n                      backgroundColor: 'rgba(156, 163, 175, 0.1)'\n                    },\n                    '&:focus': {\n                      outline: 'none'\n                    },\n                    '&:disabled': {\n                      color: 'rgba(156, 163, 175, 0.6)',\n                      cursor: 'not-allowed'\n                    },\n                    transition: 'all 0.2s',\n                    padding: '8px'\n                  },\n                  children: isGeneratingPDF ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 16,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2717,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                    fontSize: \"medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2719,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2697,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2696,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2695,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2664,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2663,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2660,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto bg-gray-200\",\n        style: {\n          marginTop: isConnected ? '60px' : '0px',\n          width: '100%'\n        },\n        children: [!isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 bg-white shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Back to Reports\",\n            placement: \"bottom\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackToDashboard,\n              sx: {\n                color: 'rgb(75, 85, 99)',\n                padding: '6px',\n                '&:hover': {\n                  backgroundColor: 'rgba(75, 85, 99, 0.1)'\n                },\n                '&:focus': {\n                  outline: 'none'\n                },\n                transition: 'all 0.2s'\n              },\n              children: /*#__PURE__*/_jsxDEV(ArrowBack, {\n                fontSize: \"medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2752,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2738,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2737,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2736,\n          columnNumber: 13\n        }, this), isLoadingData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2761,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 text-lg text-gray-600\",\n              children: \"Loading report data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2762,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2760,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2759,\n          columnNumber: 3\n        }, this) : dataError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: isCheckingConnection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 40\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2770,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 text-lg text-gray-600\",\n                children: \"Checking connection status...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2771,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true) : qboConnectionStatus && qboConnectionStatus.connectionStatus !== 'CONNECTED' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg text-red-600 mb-2\",\n                children: \"Company Disconnected from QuickBooks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2775,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 mb-3\",\n                children: \"Please connect your company to QuickBooks to generate reports.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2776,\n                columnNumber: 11\n              }, this), redirectCountdown !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500 mb-3\",\n                children: [\"Redirecting you to company connection page in \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-blue-600\",\n                  children: redirectCountdown\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2783,\n                  columnNumber: 61\n                }, this), \" seconds\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2782,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleRedirectToConnection,\n                className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-sm font-medium\",\n                children: \"GO TO COMPANY INFO\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2787,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg text-red-600 mb-4\",\n                children: \"Failed to load report data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2796,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: fetchReportData,\n                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                children: \"Retry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2797,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2767,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2766,\n          columnNumber: 3\n        }, this) : isConnected ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(DeepSightCoverPage, {\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2811,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(TableOfContents, {\n            headingTextStyle: getHeadingStyle(),\n            headerTextStyle: getHeaderStyle(),\n            contentTextStyle: getContentStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2815,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(ReportSummary, {\n            headerTextStyle: getHeaderStyle(),\n            headingTextStyle: getHeadingStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            h2TextStyle: getH2Style(),\n            h3TextStyle: getH3Style(),\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2823,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(FiscalYearDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            fiscalData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2832,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(ExpenseSummaryDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2839,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(OperationalEfficiencyDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            operationalData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2846,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(LiquiditySummaryDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            liquidityData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2853,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLoss13MonthDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2860,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLossMonthlyDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2867,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLossYTDDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2874,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(BalanceSheetDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2881,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2730,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2657,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1447,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomizeTemplateWithPreview, \"B34vdr/t5Wd9ojbbxdBfY3J3iKo=\", false, function () {\n  return [useSearchParams, useParams, useNavigate];\n});\n_c = CustomizeTemplateWithPreview;\nexport default CustomizeTemplateWithPreview;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplateWithPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "IconButton", "<PERSON><PERSON><PERSON>", "CircularProgress", "Box", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "Select", "MenuItem", "InputLabel", "FormControl", "TextField", "Sync", "SyncIcon", "ArrowBack", "Download", "DownloadIcon", "Snackbar", "<PERSON><PERSON>", "useSearchParams", "TableOfContents", "ReportSummary", "FiscalYearDashboard", "ExpenseSummaryDashboard", "OperationalEfficiencyDashboard", "LiquiditySummaryDashboard", "ProfitLoss13MonthDashboard", "ProfitLossMonthlyDashboard", "ProfitLossYTDDashboard", "BalanceSheetDashboard", "useNavigate", "useParams", "downloadPDFFromBase64", "getContentSettings", "checkQBOConnectionStatus", "getTemplateSettings", "updateTemplateSettings", "generateReportCalculation", "uploadChartsToS3", "getGlobalSettings", "DeepSightCoverPage", "Apex<PERSON><PERSON><PERSON>", "InfoOutlined", "InfoIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomizeTemplateWithPreview", "_s", "searchParams", "params", "companyId", "id", "reportId", "reportData", "setReportData", "isLoadingData", "setIsLoadingData", "dataError", "setDataError", "templateSettings", "setTemplateSettings", "initialSettings", "setInitialSettings", "isLoadingSettings", "setIsLoadingSettings", "settingsError", "setSettingsError", "currentUser", "setCurrentUser", "isLoadingUser", "setIsLoadingUser", "contentSettings", "setContentSettings", "isLoadingContentSettings", "setIsLoadingContentSettings", "contentSettingsError", "setContentSettingsError", "showSuccess", "setShowSuccess", "successMessage", "setSuccessMessage", "selectedTemplate", "setSelectedTemplate", "isGeneratingPDF", "setIsGeneratingPDF", "isSavingSettings", "setIsSavingSettings", "isResettingSettings", "setIsResettingSettings", "showSaveConfirmModal", "setShowSaveConfirmModal", "showResetConfirmModal", "setShowResetConfirmModal", "qboConnectionStatus", "setQboConnectionStatus", "isCheckingConnection", "setIsCheckingConnection", "redirectCountdown", "setRedirectCountdown", "redirectTimerRef", "setRedirectTimerRef", "navigate", "defaultSettings", "header", "fontStyle", "fontType", "fontSize", "color", "heading", "h2", "h3", "subHeading", "content", "fontStyles", "fontTypes", "fontSizeConstraints", "min", "max", "fetchContentSettings", "response", "data", "success", "settingsData", "Error", "error", "console", "message", "defaultContentSettings", "chartSettings", "incomeSummary", "netIncome", "grossProfitMargin", "netProfitMargin", "roaAndRoe", "checkQBOConnectionStatusLocal", "connectionStatus", "fetchTemplateSettings", "settings", "userData", "user", "localStorage", "setItem", "JSON", "stringify", "savedSettings", "getItem", "fallbackSettings", "parse", "initializeSettings", "urlSettings", "get", "parsedSettings", "fetchReportData", "addPaddingToImage", "imageDataURI", "padding", "Promise", "resolve", "reject", "img", "Image", "onload", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "fillStyle", "fillRect", "strokeStyle", "lineWidth", "strokeRect", "drawImage", "paddedDataURI", "toDataURL", "onerror", "src", "chartExportConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "title", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expensesMonthlyChart", "wagesRevenueChart", "stackedColumnChart", "netIncomeChart", "grossProfitChart", "netProfitMarginChart", "salesOutstandingChart", "payablesOutstandingChart", "inventoryOutstandingChart", "cashConversionChart", "fixedAssetTurnoverChart", "netChangeChart", "quickRatioChart", "months<PERSON>ash<PERSON><PERSON>", "uploadSingleChartToS3", "chart<PERSON>ey", "chart", "window", "_chartExportConfig$ch", "setTimeout", "uri", "dataURI", "type", "scale", "quality", "imgURI", "paddedImageDataURI", "config", "chartData", "chartUniqueName", "chartName", "_chartExportConfig$ch2", "uploadChartsInBackground", "chartResults", "chartKeys", "Object", "keys", "for<PERSON>ach", "isAvailable", "warn", "result", "push", "successful", "filter", "r", "failed", "length", "chartsToUpload", "map", "actualReportId", "Date", "now", "dateRequested", "toISOString", "uploadResponse", "uploadResults", "chartError", "handleDownloadPDF", "contentPanel", "querySelector", "freezeChartsAndLegends", "container", "chartContainers", "querySelectorAll", "chartContainer", "index", "svgElements", "svgEl", "computedStyle", "getComputedStyle", "tagName", "toLowerCase", "style", "overflow", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "containerRect", "getBoundingClientRect", "position", "apexElements", "element", "top", "left", "right", "bottom", "display", "visibility", "opacity", "gridLines", "line", "axisLines", "seriesElements", "legends", "legend", "rect", "closest", "parentRect", "relativeTop", "relativeLeft", "setAttribute", "Math", "zIndex", "containerPosition", "legendElements", "el", "fontFamily", "fontWeight", "margin", "justifyContent", "cloned<PERSON><PERSON>nt", "cloneNode", "cloned<PERSON>harts", "seenLines", "Set", "lineKey", "getAttribute", "has", "remove", "add", "minHeight", "maxHeight", "clonedLegends", "topValue", "parseFloat", "components", "component", "componentHeader", "reportHeader", "metricGrid", "hasMaxW8xl", "hasTrailingText", "textContent", "includes", "hasProfitLossTable", "isMonthTrailing", "shouldHaveRepeatingHeaders", "classList", "contains", "page", "pageBreakBefore", "setProperty", "pageBreakInside", "pageBreakAfter", "allChildren", "Array", "from", "children", "nonHeaderChildren", "child", "contentGroup", "className", "append<PERSON><PERSON><PERSON>", "tables", "table", "thead", "borderCollapse", "htmlContent", "innerHTML", "generatePDFFromHTML", "format", "orientation", "printBackground", "preferCSSPageSize", "pdf", "filename", "split", "downloadSuccess", "initializeDataWithConnectionCheck", "startRedirectTimer", "clearRedirectTimer", "handleBackToDashboard", "state", "activeTab", "handleSettingChange", "section", "property", "value", "constraints", "prev", "handleCloseSuccess", "saveSettingsToAPI", "handleSave", "handleConfirmSave", "handleCancelSave", "handleResetToDefault", "handleRedirectToConnection", "timerId", "setInterval", "clearInterval", "handleConfirmReset", "resetSettings", "resetMessage", "isAdmin", "globalResponse", "globalError", "handleCancelReset", "handleResync", "getFontWeight", "weights", "getHeaderStyle", "_templateSettings$hea", "_templateSettings$hea2", "_templateSettings$hea3", "_templateSettings$hea4", "borderRadius", "getHeadingStyle", "_templateSettings$hea5", "_templateSettings$hea6", "_templateSettings$hea7", "_templateSettings$hea8", "getH2Style", "_templateSettings$h", "_templateSettings$h2", "_templateSettings$h3", "_templateSettings$h4", "getH3Style", "_templateSettings$h5", "_templateSettings$h6", "_templateSettings$h7", "_templateSettings$h8", "getContentStyle", "_templateSettings$con", "_templateSettings$con2", "_templateSettings$con3", "_templateSettings$con4", "lineHeight", "getSubHeadingStyle", "_templateSettings$sub", "_templateSettings$sub2", "_templateSettings$sub3", "_templateSettings$sub4", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isConnected", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "variant", "sx", "backgroundColor", "onClick", "disabled", "mr", "placement", "marginRight", "outline", "transition", "arrow", "marginBottom", "fullWidth", "onChange", "e", "target", "borderColor", "font", "inputProps", "parseInt", "cursor", "marginLeft", "alignItems", "gap", "marginTop", "headingTextStyle", "headerTextStyle", "contentTextStyle", "subHeadingTextStyle", "h2TextStyle", "h3TextStyle", "fiscalData", "operationalData", "liquidityData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/CustomizeReport.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { IconButton, Tooltip, CircularProgress, Box, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button, Select, MenuItem, InputLabel, FormControl, TextField } from '@mui/material'; import { Sync as SyncIcon, ArrowBack, Download as DownloadIcon } from '@mui/icons-material';\r\nimport { Snackbar, Alert } from '@mui/material';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport TableOfContents from './ReportPages/TableOfContents';\r\nimport ReportSummary from './ReportPages/ReportSummary';\r\nimport FiscalYearDashboard from './ReportPages/FiscalYear';\r\nimport ExpenseSummaryDashboard from './ReportPages/ExpenseSummary';\r\nimport OperationalEfficiencyDashboard from './ReportPages/OperationalEfficiency';\r\nimport LiquiditySummaryDashboard from './ReportPages/LiquiditySummary';\r\nimport ProfitLoss13MonthDashboard from './ReportPages/MonthTrailing';\r\nimport ProfitLossMonthlyDashboard from './ReportPages/Monthly';\r\nimport ProfitLossYTDDashboard from './ReportPages/YearToDate';\r\nimport BalanceSheetDashboard from './ReportPages/BalanceSheet';\r\nimport { useNavigate, useParams } from 'react-router-dom';\r\nimport { downloadPDFFromBase64 } from '../../services/pdf';\r\nimport {\r\n  getContentSettings,\r\n  checkQBOConnectionStatus,\r\n  getTemplateSettings,\r\n  updateTemplateSettings,\r\n  generateReportCalculation,\r\n  uploadChartsToS3,\r\n  getGlobalSettings\r\n} from '../../services/customizeReportService';\r\nimport DeepSightCoverPage from './ReportPages/CoverPage';\r\nimport ApexCharts from 'apexcharts';\r\nimport { InfoOutlined as InfoIcon } from '@mui/icons-material';\r\n\r\n\r\n\r\n\r\nconst CustomizeTemplateWithPreview = () => {\r\n  const [searchParams] = useSearchParams();\r\n\r\n  const params = useParams();\r\n  const companyId = params.id;\r\n  const reportId = params.reportId;\r\n\r\n  // API Data State\r\n  const [reportData, setReportData] = useState(null);\r\n  const [isLoadingData, setIsLoadingData] = useState(true);\r\n  const [dataError, setDataError] = useState(null);\r\n\r\n  // Template Settings State\r\n  const [templateSettings, setTemplateSettings] = useState(null);\r\n  const [initialSettings, setInitialSettings] = useState(null); // Store initial settings for reset\r\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\r\n  const [settingsError, setSettingsError] = useState(null);\r\n\r\n  // User State\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [isLoadingUser, setIsLoadingUser] = useState(true);\r\n\r\n  // Content Settings State (NEW)\r\n  const [contentSettings, setContentSettings] = useState(null);\r\n  const [isLoadingContentSettings, setIsLoadingContentSettings] = useState(true);\r\n  const [contentSettingsError, setContentSettingsError] = useState(null);\r\n\r\n  // UI State\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [selectedTemplate, setSelectedTemplate] = useState('Deepsight');\r\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\r\n  const [isSavingSettings, setIsSavingSettings] = useState(false);\r\n  const [isResettingSettings, setIsResettingSettings] = useState(false);\r\n\r\n  // Modal State\r\n  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);\r\n  const [showResetConfirmModal, setShowResetConfirmModal] = useState(false);\r\n\r\n  //qbo connection status\r\nconst [qboConnectionStatus, setQboConnectionStatus] = useState(null);\r\nconst [isCheckingConnection, setIsCheckingConnection] = useState(false);\r\n\r\n// Auto-redirect timer state\r\nconst [redirectCountdown, setRedirectCountdown] = useState(null);\r\nconst [redirectTimerRef, setRedirectTimerRef] = useState(null);\r\n\r\n  const navigate = useNavigate();\r\n\r\n  //fetching company id for report data\r\n\r\n  // Default settings fallback\r\n  const defaultSettings = {\r\n    header: {\r\n      fontStyle: 'Open Sans',\r\n      fontType: 'Bold',\r\n      fontSize: 44,\r\n      color: '#1e7c8c'\r\n    },\r\n    heading: {\r\n      fontStyle: 'Open Sans',\r\n      fontType: 'Bold',\r\n      fontSize: 24,\r\n      color: '#1e7c8c'\r\n    },\r\n    h2 : {\r\n      fontStyle: \"Open Sans\",\r\n      fontType : \"Bold\",\r\n      fontSize : 24,\r\n      color : '#333333'\r\n    },\r\n    h3 : {\r\n      fontStyle : \"Open Sans\",\r\n      fontType : \"Bold\",\r\n      fontSize : 18,\r\n      color : '#333333'\r\n    },\r\n    subHeading: {\r\n      fontStyle: 'Open Sans',\r\n      fontType: 'Bold',\r\n      fontSize: 28,\r\n      color: '#1e7c8c'\r\n    },\r\n    content: {\r\n      fontStyle: 'Open Sans',\r\n      fontType: 'Regular',\r\n      fontSize: 16,\r\n      color: '#333333'\r\n    }\r\n  };\r\n\r\n  const fontStyles = [\"Open Sans\", 'Calibri', 'Arial', 'Times New Roman', 'Georgia', 'Verdana', 'Helvetica'];\r\n  const fontTypes = ['Regular', 'Bold'];\r\n\r\n  // Font size constraints\r\n  const fontSizeConstraints = {\r\n    header: { min: 32, max: 48 },\r\n    heading: { min: 20, max: 40 },\r\n    h2 : {min : 20, max : 40 },\r\n    h3 : {min : 15, max : 28}, \r\n    subHeading: { min: 18, max: 28 },\r\n    content: { min: 9, max: 20 }\r\n  };\r\n\r\n  // Fetch content settings from API (NEW)\r\n  const fetchContentSettings = async () => {\r\n    try {\r\n      setIsLoadingContentSettings(true);\r\n      setContentSettingsError(null);\r\n\r\n      const response = await getContentSettings(companyId, 'DEEPSIGHT');\r\n\r\n      if (response.data && response.data.success) {\r\n        const settingsData = response.data.data;\r\n        setContentSettings(settingsData);\r\n      } else {\r\n        throw new Error('Failed to fetch content settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching content settings:', error);\r\n      setContentSettingsError(error.message);\r\n\r\n      // Set default content settings if API fails\r\n      const defaultContentSettings = {\r\n        chartSettings: {\r\n          incomeSummary: true,\r\n          netIncome: true,\r\n          grossProfitMargin: true,\r\n          netProfitMargin: true,\r\n          roaAndRoe: true,\r\n        }\r\n      };\r\n      setContentSettings(defaultContentSettings);\r\n\r\n      setSuccessMessage('Using default chart settings. Failed to load from server.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsLoadingContentSettings(false);\r\n    }\r\n  };\r\n\r\n  //check qbo connection status\r\n  const checkQBOConnectionStatusLocal = async () => {\r\n  try {\r\n    setIsCheckingConnection(true);\r\n    const response = await checkQBOConnectionStatus(companyId);\r\n\r\n    if (response.data && response.data.success) {\r\n      setQboConnectionStatus(response.data.data);\r\n      return response.data.data;\r\n    } else {\r\n      throw new Error('Failed to check QBO connection status');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error checking QBO connection status:', error);\r\n    // Assume disconnected if API call fails\r\n    setQboConnectionStatus({ connectionStatus: 'DISCONNECTED' });\r\n    return { connectionStatus: 'DISCONNECTED' };\r\n  } finally {\r\n    setIsCheckingConnection(false);\r\n  }\r\n};\r\n\r\n  // Fetch template settings from API\r\n  const fetchTemplateSettings = async () => {\r\n    try {\r\n      setIsLoadingSettings(true);\r\n      setIsLoadingUser(true);\r\n      setSettingsError(null);\r\n\r\n      const response = await getTemplateSettings();\r\n\r\n      if (response.data && response.data.success) {\r\n        const settingsData = response.data.data.settings;\r\n        const userData = response.data.user;\r\n\r\n        // Store settings in localStorage\r\n        localStorage.setItem('templateSettings', JSON.stringify(settingsData));\r\n\r\n        // Set both current and initial settings\r\n        setTemplateSettings(settingsData);\r\n        setInitialSettings(settingsData);\r\n\r\n        // Set user information from the API response\r\n        if (userData) {\r\n          setCurrentUser(userData);\r\n        }\r\n      } else {\r\n        throw new Error('Failed to fetch template settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching template settings:', error);\r\n      setSettingsError(error.message);\r\n\r\n      // Fallback to localStorage or default settings\r\n      const savedSettings = localStorage.getItem('templateSettings');\r\n      const fallbackSettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\r\n\r\n      setTemplateSettings(fallbackSettings);\r\n      setInitialSettings(fallbackSettings);\r\n\r\n      setSuccessMessage('Using cached settings. Failed to load from server.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsLoadingSettings(false);\r\n      setIsLoadingUser(false);\r\n    }\r\n  };\r\n\r\n  // Load settings from URL params (for PDF generation) or fetch from API\r\n  const initializeSettings = () => {\r\n    try {\r\n      // First check if settings are in URL (for PDF generation)\r\n      const urlSettings = searchParams.get('templateSettings');\r\n      if (urlSettings) {\r\n        const parsedSettings = JSON.parse(urlSettings);\r\n        setTemplateSettings(parsedSettings);\r\n        setInitialSettings(parsedSettings);\r\n        setIsLoadingSettings(false);\r\n        return;\r\n      }\r\n\r\n      // Otherwise fetch from API\r\n      fetchTemplateSettings();\r\n    } catch (error) {\r\n      console.error('Error initializing settings:', error);\r\n      setTemplateSettings(defaultSettings);\r\n      setInitialSettings(defaultSettings);\r\n      setIsLoadingSettings(false);\r\n    }\r\n  };\r\n\r\n  // Update the fetchReportData function to use the companyId from params\r\nconst fetchReportData = async () => {\r\n  try {\r\n    setIsLoadingData(true);\r\n    setDataError(null);\r\n\r\n    // Check QBO connection status first\r\n    const connectionStatus = await checkQBOConnectionStatusLocal();\r\n\r\n    // Only proceed if connected\r\n    if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\r\n      const response = await generateReportCalculation(companyId, reportId);\r\n\r\n      if (response.data && response.data.success) {\r\n        setReportData(response.data.data);\r\n      } else {\r\n        throw new Error('Failed to fetch report data');\r\n      }\r\n    } else {\r\n      // If not connected, set error state\r\n      setDataError('Company not connected to QuickBooks');\r\n      setSuccessMessage('Company is not connected to QuickBooks. Please connect to generate reports.');\r\n      setShowSuccess(true);\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching report data:', error);\r\n    setDataError(error.message);\r\n\r\n    setSuccessMessage('Failed to load report data. Please try again.');\r\n    setShowSuccess(true);\r\n  } finally {\r\n    setIsLoadingData(false);\r\n  }\r\n};\r\n  // Generate and download PDF\r\n  // In CustomizeReport.jsx, update the handleDownloadPDF function\r\n\r\n  // Function to add padding around chart images with enhanced styling\r\n  const addPaddingToImage = (imageDataURI, padding = 40) => {\r\n    return new Promise((resolve, reject) => {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        try {\r\n          // Create canvas with padding\r\n          const canvas = document.createElement('canvas');\r\n          const ctx = canvas.getContext('2d');\r\n\r\n          // Set canvas size with padding\r\n          canvas.width = img.width + (padding * 2);\r\n          canvas.height = img.height + (padding * 2);\r\n\r\n          // Fill background with white\r\n          ctx.fillStyle = '#ffffff';\r\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n          // Add subtle border (optional)\r\n          ctx.strokeStyle = '#e5e7eb';\r\n          ctx.lineWidth = 1;\r\n          ctx.strokeRect(padding - 1, padding - 1, img.width + 2, img.height + 2);\r\n\r\n          // Draw the chart image with padding offset\r\n          ctx.drawImage(img, padding, padding);\r\n\r\n          // Convert to data URI with high quality\r\n          const paddedDataURI = canvas.toDataURL('image/png', 0.95);\r\n          resolve(paddedDataURI);\r\n        } catch (error) {\r\n          reject(error);\r\n        }\r\n      };\r\n\r\n      img.onerror = () => {\r\n        reject(new Error('Failed to load chart image'));\r\n      };\r\n\r\n      img.src = imageDataURI;\r\n    });\r\n  };\r\n\r\n  // Chart export configuration\r\n  const chartExportConfig = {\r\n    // ExpenseSummary charts\r\n    roaRoeChart: { name: 'ROA_ROE_Chart', title: 'ROA & ROE Chart' },\r\n    expensesPieChart: { name: 'Expenses_Pie_Chart', title: 'Expenses Pie Chart' },\r\n    expensesMonthlyChart: { name: 'Expenses_Top_Accounts_Monthly_Chart', title: 'Expenses: Top Accounts Monthly' },\r\n    wagesRevenueChart: { name: 'Wages_Revenue_Chart', title: 'Wages vs Revenue Chart' },\r\n\r\n    // FiscalYear charts\r\n    stackedColumnChart: { name: 'Monthly_Performance_Chart', title: 'Monthly Performance Breakdown' },\r\n    netIncomeChart: { name: 'Net_Income_Chart', title: 'Net Income Chart' },\r\n    grossProfitChart: { name: 'Gross_Profit_Margin_Chart', title: 'Gross Profit Margin' },\r\n    netProfitMarginChart: { name: 'Net_Profit_Margin_Chart', title: 'Net Profit Margin Chart' },\r\n\r\n    // OperationalEfficiency charts\r\n    salesOutstandingChart: { name: 'Days_Sales_Outstanding_Chart', title: 'Days Sales (A/R) Outstanding' },\r\n    payablesOutstandingChart: { name: 'Days_Payables_Outstanding_Chart', title: 'Days Payables (AP) Outstanding' },\r\n    inventoryOutstandingChart: { name: 'Days_Inventory_Outstanding_Chart', title: 'Days Inventory Outstanding' },\r\n    cashConversionChart: { name: 'Cash_Conversion_Cycle_Chart', title: 'Cash Conversion Cycle' },\r\n    fixedAssetTurnoverChart: { name: 'Fixed_Asset_Turnover_Chart', title: 'Fixed Asset Turnover Chart' },\r\n\r\n    // LiquiditySummary charts\r\n    netChangeChart: { name: 'Net_Change_Cash_Chart', title: 'Net Change in Cash Chart' },\r\n    quickRatioChart: { name: 'Quick_Ratio_Chart', title: 'Quick Ratio Chart' },\r\n    monthsCashChart: { name: 'Months_Cash_Chart', title: 'Months Cash on Hand Chart' }\r\n  };\r\n\r\n  // Generic function to upload a single chart to S3\r\n  const uploadSingleChartToS3 = async (chartKey) => {\r\n    try {\r\n      const chart = window[chartKey];\r\n      if (!chart) {\r\n        throw new Error(`${chartExportConfig[chartKey]?.title || chartKey} not found or not rendered.`);\r\n      }\r\n\r\n      // Wait for chart to be stable\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      // Use ApexCharts built-in export functionality\r\n      const uri = await chart.dataURI({\r\n        type: 'png',\r\n        width: 1200,\r\n        height: 600,\r\n        scale: 1,\r\n        quality: 0.95\r\n      });\r\n\r\n      if (!uri || !uri.imgURI) {\r\n        throw new Error('Failed to generate chart image data.');\r\n      }\r\n\r\n      // Add padding to the image\r\n      const paddedImageDataURI = await addPaddingToImage(uri.imgURI, 40);\r\n\r\n      // Prepare chart data for upload\r\n      const config = chartExportConfig[chartKey];\r\n      const chartData = {\r\n        chartData: paddedImageDataURI,\r\n        chartUniqueName: config.name,\r\n        chartName: config.title\r\n      };\r\n\r\n      return { success: true, chartData, title: config.title };\r\n    } catch (error) {\r\n      console.error(`Chart preparation error for ${chartKey}:`, error);\r\n      return { success: false, error: error.message, title: chartExportConfig[chartKey]?.title || chartKey };\r\n    }\r\n  };\r\n\r\n  // Background chart upload function (non-blocking)\r\n  const uploadChartsInBackground = async () => {\r\n    try {\r\n      const chartResults = [];\r\n      const chartKeys = Object.keys(chartExportConfig);\r\n\r\n      // Debug: Log which charts are available\r\n      chartKeys.forEach(chartKey => {\r\n        const isAvailable = !!window[chartKey];\r\n        if (!isAvailable) {\r\n          console.warn(`Background: Missing chart: ${chartKey} - ${chartExportConfig[chartKey].title}`);\r\n        }\r\n      });\r\n\r\n      // Prepare all chart data\r\n      for (const chartKey of chartKeys) {\r\n        if (window[chartKey]) {\r\n          const result = await uploadSingleChartToS3(chartKey);\r\n          chartResults.push(result);\r\n\r\n          // Small delay between preparations\r\n          await new Promise(resolve => setTimeout(resolve, 200));\r\n        } else {\r\n          console.warn(`Background: Skipping ${chartKey} - chart not found or not rendered`);\r\n        }\r\n      }\r\n\r\n      const successful = chartResults.filter(r => r.success);\r\n      const failed = chartResults.filter(r => !r.success);\r\n\r\n      if (successful.length > 0) {\r\n        // Upload charts to S3\r\n        const chartsToUpload = successful.map(result => result.chartData);\r\n\r\n        // Use the actual report ID from URL params, fallback to timestamp if not available\r\n        const actualReportId = reportId || Date.now();\r\n        const dateRequested = new Date().toISOString();\r\n\r\n        const uploadResponse = await uploadChartsToS3(chartsToUpload, companyId, actualReportId, dateRequested);\r\n\r\n        if (uploadResponse.data.success) {\r\n          const uploadResults = uploadResponse.data.data;\r\n          // // Show subtle notification that charts were uploaded (optional)\r\n          // setTimeout(() => {\r\n          //   // setSuccessMessage(`Charts uploaded to S3 successfully (${uploadResults.successful} files)`);\r\n          //   setShowSuccess(true);\r\n          // }, 2000); // Show after 2 seconds\r\n\r\n        } else {\r\n          console.error('Background: Failed to upload charts to S3');\r\n        }\r\n      }\r\n    } catch (chartError) {\r\n      console.error('Background: Chart upload error:', chartError);\r\n      // Silently handle errors in background process\r\n    }\r\n  };\r\n\r\n  // Note: Individual chart export functions removed as charts are now exported with PDF\r\n\r\n  const handleDownloadPDF = async () => {\r\n    try {\r\n      setIsGeneratingPDF(true);\r\n      uploadChartsInBackground();\r\n      setSuccessMessage('Generating PDF...');\r\n      setShowSuccess(true);\r\n\r\n      // Get the scrollable content panel that contains all components\r\n      const contentPanel = document.querySelector('.flex-1.overflow-y-auto.bg-gray-200');\r\n      if (!contentPanel) {\r\n        throw new Error('Could not find report content to generate PDF');\r\n      }\r\n\r\n      // Wait for charts to fully render before capturing content\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      // FREEZE CHART ELEMENTS AND LEGENDS BEFORE CLONING\r\n      const freezeChartsAndLegends = (container) => {\r\n        // 1. Freeze Chart Elements\r\n        const chartContainers = container.querySelectorAll('div[id*=\"apex\"]');\r\n\r\n        chartContainers.forEach((chartContainer, index) => {\r\n          try {\r\n            // Freeze all SVG elements within the chart\r\n            const svgElements = chartContainer.querySelectorAll('svg, svg *');\r\n            svgElements.forEach(svgEl => {\r\n              // Get computed styles and apply them inline\r\n              const computedStyle = window.getComputedStyle(svgEl);\r\n\r\n              // Apply critical styles inline to prevent changes\r\n              if (svgEl.tagName.toLowerCase() === 'svg') {\r\n                svgEl.style.width = computedStyle.width;\r\n                svgEl.style.height = computedStyle.height;\r\n                svgEl.style.overflow = 'visible';\r\n              }\r\n\r\n              // Freeze stroke and fill properties\r\n              if (computedStyle.stroke && computedStyle.stroke !== 'none') {\r\n                svgEl.style.stroke = computedStyle.stroke;\r\n              }\r\n              if (computedStyle.strokeWidth && computedStyle.strokeWidth !== '0px') {\r\n                svgEl.style.strokeWidth = computedStyle.strokeWidth;\r\n              }\r\n              if (computedStyle.fill && computedStyle.fill !== 'none') {\r\n                svgEl.style.fill = computedStyle.fill;\r\n              }\r\n              if (computedStyle.strokeDasharray && computedStyle.strokeDasharray !== 'none') {\r\n                svgEl.style.strokeDasharray = computedStyle.strokeDasharray;\r\n              }\r\n\r\n              // Prevent any transformations from changing\r\n              if (computedStyle.transform && computedStyle.transform !== 'none') {\r\n                svgEl.style.transform = computedStyle.transform;\r\n              }\r\n            });\r\n\r\n            // Freeze the chart container dimensions and position\r\n            const containerRect = chartContainer.getBoundingClientRect();\r\n            chartContainer.style.width = containerRect.width + 'px';\r\n            chartContainer.style.height = containerRect.height + 'px';\r\n            chartContainer.style.position = 'relative';\r\n            chartContainer.style.overflow = 'visible';\r\n\r\n            // Freeze all ApexCharts specific elements\r\n            const apexElements = chartContainer.querySelectorAll('[class*=\"apexcharts\"]');\r\n            apexElements.forEach(element => {\r\n              const computedStyle = window.getComputedStyle(element);\r\n\r\n              // Preserve positioning\r\n              if (computedStyle.position && computedStyle.position !== 'static') {\r\n                element.style.position = computedStyle.position;\r\n                element.style.top = computedStyle.top;\r\n                element.style.left = computedStyle.left;\r\n                element.style.right = computedStyle.right;\r\n                element.style.bottom = computedStyle.bottom;\r\n              }\r\n\r\n              // Preserve dimensions\r\n              element.style.width = computedStyle.width;\r\n              element.style.height = computedStyle.height;\r\n\r\n              // Preserve display and visibility\r\n              element.style.display = computedStyle.display;\r\n              element.style.visibility = computedStyle.visibility;\r\n              element.style.opacity = computedStyle.opacity;\r\n            });\r\n\r\n            // Specifically handle grid lines and axis elements that might be causing extra lines\r\n            const gridLines = chartContainer.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\r\n            gridLines.forEach(line => {\r\n              const computedStyle = window.getComputedStyle(line);\r\n              line.style.stroke = computedStyle.stroke;\r\n              line.style.strokeWidth = computedStyle.strokeWidth;\r\n              line.style.strokeDasharray = computedStyle.strokeDasharray;\r\n              line.style.opacity = computedStyle.opacity;\r\n            });\r\n\r\n            // Handle axis lines\r\n            const axisLines = chartContainer.querySelectorAll('.apexcharts-xaxis line, .apexcharts-yaxis line');\r\n            axisLines.forEach(line => {\r\n              const computedStyle = window.getComputedStyle(line);\r\n              line.style.stroke = computedStyle.stroke;\r\n              line.style.strokeWidth = computedStyle.strokeWidth;\r\n              line.style.opacity = computedStyle.opacity;\r\n            });\r\n\r\n            // Handle data series paths/bars\r\n            const seriesElements = chartContainer.querySelectorAll('.apexcharts-series path, .apexcharts-series rect, .apexcharts-series circle');\r\n            seriesElements.forEach(element => {\r\n              const computedStyle = window.getComputedStyle(element);\r\n              if (computedStyle.fill) element.style.fill = computedStyle.fill;\r\n              if (computedStyle.stroke) element.style.stroke = computedStyle.stroke;\r\n              if (computedStyle.strokeWidth) element.style.strokeWidth = computedStyle.strokeWidth;\r\n              if (computedStyle.opacity) element.style.opacity = computedStyle.opacity;\r\n            });\r\n          } catch (error) {\r\n            console.error(`Error freezing chart ${index}:`, error);\r\n          }\r\n        });\r\n\r\n        // 2. Freeze Legend Elements (your existing legend freezing code)\r\n        const legends = container.querySelectorAll('.apexcharts-legend');\r\n\r\n        legends.forEach((legend, index) => {\r\n          try {\r\n            // Get current position relative to viewport\r\n            const rect = legend.getBoundingClientRect();\r\n\r\n            // Find the closest chart container or parent container\r\n            let chartContainer = legend.closest('div[id*=\"apex\"]');\r\n            if (!chartContainer) {\r\n              // Look for chart section container\r\n              chartContainer = legend.closest('.bg-white.p-6');\r\n            }\r\n            if (!chartContainer) {\r\n              // Fallback to component container\r\n              chartContainer = legend.closest('.min-h-screen');\r\n            }\r\n\r\n            if (chartContainer) {\r\n              const parentRect = chartContainer.getBoundingClientRect();\r\n\r\n              // Calculate position relative to the chart container\r\n              const relativeTop = rect.top - parentRect.top;\r\n              const relativeLeft = rect.left - parentRect.left;\r\n\r\n              // Store original styles for debugging\r\n              legend.setAttribute('data-original-position', legend.style.position || '');\r\n              legend.setAttribute('data-original-top', legend.style.top || '');\r\n              legend.setAttribute('data-original-left', legend.style.left || '');\r\n\r\n              // Set the legend to absolute positioning relative to its chart container\r\n              legend.style.position = 'absolute';\r\n              legend.style.top = Math.max(0, relativeTop) + 'px';\r\n              legend.style.left = relativeLeft + 'px';\r\n              legend.style.right = 'auto';\r\n              legend.style.bottom = 'auto';\r\n              legend.style.transform = 'none';\r\n              legend.style.zIndex = '10';\r\n\r\n              // Ensure the chart container has relative positioning\r\n              const containerPosition = window.getComputedStyle(chartContainer).position;\r\n              if (containerPosition === 'static') {\r\n                chartContainer.style.position = 'relative';\r\n              }\r\n\r\n              // Freeze legend's internal styling\r\n              const legendElements = legend.querySelectorAll('*');\r\n              legendElements.forEach(el => {\r\n                const computedStyle = window.getComputedStyle(el);\r\n                if (computedStyle.color) el.style.color = computedStyle.color;\r\n                if (computedStyle.fontSize) el.style.fontSize = computedStyle.fontSize;\r\n                if (computedStyle.fontFamily) el.style.fontFamily = computedStyle.fontFamily;\r\n                if (computedStyle.fontWeight) el.style.fontWeight = computedStyle.fontWeight;\r\n              });\r\n            } else {\r\n              console.warn(`Could not find chart container for legend ${index}`);\r\n\r\n              // Fallback: just prevent the legend from moving by setting fixed position\r\n              legend.style.position = 'relative';\r\n              legend.style.top = '20px';\r\n              legend.style.left = 'auto';\r\n              legend.style.right = 'auto';\r\n              legend.style.transform = 'none';\r\n              legend.style.margin = '20px auto';\r\n              legend.style.display = 'flex';\r\n              legend.style.justifyContent = 'center';\r\n            }\r\n\r\n          } catch (error) {\r\n            console.error(`Error freezing legend ${index}:`, error);\r\n\r\n            // Emergency fallback - just make it static\r\n            legend.style.position = 'static';\r\n            legend.style.margin = '20px auto';\r\n            legend.style.display = 'flex';\r\n            legend.style.justifyContent = 'center';\r\n          }\r\n        });\r\n      };\r\n\r\n      // Apply the freezing function\r\n      freezeChartsAndLegends(contentPanel);\r\n\r\n      // Wait a bit more for positions to settle after freezing\r\n      await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n      // Now clone the content with frozen chart and legend positions\r\n      const clonedContent = contentPanel.cloneNode(true);\r\n\r\n      // Additional safety check: ensure no extra lines are added during cloning\r\n      const clonedCharts = clonedContent.querySelectorAll('div[id*=\"apex\"]');\r\n      clonedCharts.forEach((chart, index) => {\r\n        // Remove any duplicate or extra grid lines that might have been created\r\n        const gridLines = chart.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\r\n        const seenLines = new Set();\r\n\r\n        gridLines.forEach(line => {\r\n          const lineKey = `${line.getAttribute('x1')}-${line.getAttribute('y1')}-${line.getAttribute('x2')}-${line.getAttribute('y2')}`;\r\n          if (seenLines.has(lineKey)) {\r\n            // Duplicate line, remove it\r\n            line.remove();\r\n          } else {\r\n            seenLines.add(lineKey);\r\n          }\r\n        });\r\n\r\n        // Ensure chart maintains its frozen dimensions\r\n        chart.style.minHeight = 'auto';\r\n        chart.style.maxHeight = 'none';\r\n      });\r\n\r\n      // Additional safety check: fix any legends that might still be mispositioned in cloned content\r\n      const clonedLegends = clonedContent.querySelectorAll('.apexcharts-legend');\r\n      clonedLegends.forEach((legend, index) => {\r\n        // If legend still has a very high top value, reset it\r\n        const topValue = parseFloat(legend.style.top) || 0;\r\n        if (topValue > 500) {\r\n          legend.style.top = '20px';\r\n          legend.style.position = 'relative';\r\n          legend.style.margin = '20px auto 10px auto';\r\n          legend.style.display = 'flex';\r\n          legend.style.justifyContent = 'center';\r\n        }\r\n      });\r\n\r\n      // Find all components and modify them for proper header repetition\r\n      const components = clonedContent.querySelectorAll('.min-h-screen');\r\n      components.forEach((component) => {\r\n        const componentHeader = component.querySelector('.component-header');\r\n        const reportHeader = component.querySelector('.report-header');\r\n        const metricGrid = component.querySelector(\".metrics-flex\");\r\n\r\n        let header = componentHeader || reportHeader;\r\n\r\n        // Check if this is the MonthTrailing component by looking for the unique max-w-8xl class\r\n        // Also check for the specific text content as a fallback, but only if it has the right structure\r\n        const hasMaxW8xl = component.querySelector('.max-w-8xl') !== null;\r\n        const hasTrailingText = component.textContent.includes('13 Month Trailing');\r\n        const hasProfitLossTable = component.querySelector('.profit-loss-table') !== null;\r\n\r\n        const isMonthTrailing = hasMaxW8xl || (hasTrailingText && hasProfitLossTable);\r\n\r\n        // Check if this component should have repeating headers\r\n        // MonthTrailing (max-w-8xl), Monthly (max-w-6xl + profit-loss-section), YearToDate (max-w-6xl + profit-loss-section), BalanceSheet\r\n        const shouldHaveRepeatingHeaders = isMonthTrailing ||\r\n          (component.querySelector('.max-w-6xl') && component.classList.contains('profit-loss-section')) ||\r\n          component.textContent.includes('Balance Sheet');\r\n\r\n        if (header) {\r\n          if (!header.classList.contains('component-header')) {\r\n            header.classList.add('component-header');\r\n          }\r\n\r\n          // Add special class for MonthTrailing component\r\n          if (isMonthTrailing) {\r\n            component.classList.add('month-trailing-component');\r\n            header.classList.add('month-trailing-header');\r\n\r\n            // Add data attribute for more reliable targeting\r\n            component.setAttribute('data-component', 'month-trailing');\r\n\r\n            // Also add inline styles to ensure page orientation is applied\r\n            component.style.page = 'month-trailing';\r\n            component.style.pageBreakBefore = 'always';\r\n\r\n            // Try setting the page size directly on the component\r\n            component.style.setProperty('page', 'month-trailing', 'important');\r\n          }\r\n\r\n          // Add class for components that should have repeating headers\r\n          if (shouldHaveRepeatingHeaders) {\r\n            header.classList.add('repeating-header');\r\n\r\n            // Ensure the header structure is correct for repetition\r\n            header.style.display = 'table-header-group';\r\n            header.style.pageBreakInside = 'avoid';\r\n            header.style.pageBreakAfter = 'avoid';\r\n          }\r\n\r\n          const allChildren = Array.from(component.children);\r\n          let nonHeaderChildren;\r\n\r\n          if (componentHeader) {\r\n            nonHeaderChildren = allChildren.filter(child =>\r\n              !child.classList.contains('component-header')\r\n            );\r\n          } else if (reportHeader) {\r\n            nonHeaderChildren = allChildren.filter(child =>\r\n              !child.classList.contains('report-header')\r\n            );\r\n          }\r\n\r\n          if (nonHeaderChildren && nonHeaderChildren.length > 0) {\r\n            const contentGroup = document.createElement('div');\r\n            contentGroup.className = 'component-content';\r\n\r\n            nonHeaderChildren.forEach(child => {\r\n              contentGroup.appendChild(child);\r\n            });\r\n\r\n            component.appendChild(contentGroup);\r\n          }\r\n\r\n          component.style.display = 'table';\r\n          component.style.width = '100%';\r\n        }\r\n\r\n        // Ensure table headers repeat for components with repeating headers\r\n        if (shouldHaveRepeatingHeaders) {\r\n          const tables = component.querySelectorAll('table');\r\n          tables.forEach(table => {\r\n            const thead = table.querySelector('thead');\r\n            if (thead) {\r\n              thead.style.display = 'table-header-group';\r\n              thead.style.pageBreakInside = 'avoid';\r\n\r\n              // Add specific classes for better control\r\n              thead.classList.add('repeating-table-header');\r\n\r\n              // Ensure table structure is correct for header repetition\r\n              table.style.borderCollapse = 'collapse';\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      // Create HTML with minimal CSS changes\r\n      const htmlContent = `\r\n      <!DOCTYPE html>\r\n      <html lang=\"en\">\r\n      <head>\r\n        <meta charset=\"UTF-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Custom Report</title>\r\n        <script src=\"https://cdn.tailwindcss.com\"></script>\r\n        <link href=\"https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap\" rel=\"stylesheet\">\r\n        <style>\r\n          @page {\r\n            size: A4 portrait;\r\n            margin-top: 0in;\r\n          }\r\n\r\n          /* Special page settings for different components */\r\n          @page month-trailing {\r\n            size: letter landscape;\r\n            margin-top : 0.5in;\r\n          }\r\n\r\n          @page monthly-report {\r\n            size: A4 portrait;\r\n            margin-top : 0.5in;\r\n          }\r\n\r\n          @page year-to-date-report {\r\n            size: A4 portrait;\r\n            margin-top : 0.5in;\r\n          }\r\n\r\n          @page balance-sheet-report {\r\n            size: A4 portrait;\r\n            margin-top : 0.5in;\r\n          }\r\n\r\n          /* Alternative approach - try different page rule syntax */\r\n          @page {\r\n            size: A4 portrait;\r\n          }\r\n\r\n          /* Specific page rule for landscape components */\r\n          .month-trailing-component {\r\n            page: month-trailing;\r\n          }\r\n\r\n          @media print {\r\n            .month-trailing-component {\r\n              page: month-trailing;\r\n            }\r\n\r\n            @page month-trailing {\r\n              size: letter landscape;\r\n              margin-top : 0.5in;\r\n            }\r\n          }\r\n\r\n          body {\r\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            line-height: 1.6;\r\n            color: #374151;\r\n            background: white;\r\n          }\r\n\r\n          .page-break {\r\n            page-break-before: always;\r\n          }\r\n\r\n          .no-break {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          /* MonthTrailing component specific styles */\r\n          .month-trailing-component {\r\n            page: month-trailing;\r\n            page-break-before: always;\r\n          }\r\n\r\n          /* Force landscape orientation for month trailing components */\r\n          .month-trailing-component,\r\n          .month-trailing-component * {\r\n            page: month-trailing !important;\r\n          }\r\n\r\n          /* Alternative CSS approach for month trailing */\r\n          .max-w-8xl {\r\n            page: month-trailing;\r\n          }\r\n\r\n          /* Data attribute targeting */\r\n          [data-component=\"month-trailing\"] {\r\n            page: month-trailing !important;\r\n            page-break-before: always !important;\r\n          }\r\n\r\n          /* Multiple targeting approaches */\r\n          .month-trailing-component,\r\n          [data-component=\"month-trailing\"],\r\n          .max-w-8xl {\r\n            page: month-trailing !important;\r\n          }\r\n\r\n          /* Fallback approach using CSS transforms if @page doesn't work */\r\n          @media print {\r\n            .month-trailing-component {\r\n              page: month-trailing;\r\n              transform-origin: top left;\r\n              width: 11in;  /* US Letter width */\r\n              height: 8.5in; /* US Letter height in landscape */\r\n            }\r\n          }\r\n\r\n          /* Repeating headers for specific components */\r\n          .repeating-header {\r\n            display: table-header-group !important;\r\n            page-break-inside: avoid;\r\n            page-break-after: avoid;\r\n          }\r\n\r\n          .month-trailing-header {\r\n            display: table-header-group !important;\r\n            page-break-inside: avoid;\r\n            page-break-after: avoid;\r\n          }\r\n\r\n          /* Table headers that should repeat */\r\n          .table-header-group {\r\n            display: table-header-group !important;\r\n          }\r\n\r\n          .profit-loss-table thead {\r\n            display: table-header-group !important;\r\n          }\r\n\r\n          .repeating-table-header {\r\n            display: table-header-group !important;\r\n            page-break-inside: avoid !important;\r\n            page-break-after: avoid !important;\r\n          }\r\n\r\n          /* Ensure all table headers in components with repeating headers repeat */\r\n          .repeating-header ~ * table thead,\r\n          .month-trailing-component table thead,\r\n          .profit-loss-section table thead {\r\n            display: table-header-group !important;\r\n            page-break-inside: avoid !important;\r\n          }\r\n\r\n          /* Specific styles for different component types */\r\n          .profit-loss-section .report-header,\r\n          .balance-sheet-section .report-header {\r\n            display: table-header-group !important;\r\n            page-break-inside: avoid !important;\r\n            page-break-after: avoid !important;\r\n            margin-top : 0.5in;\r\n          }\r\n\r\n          /* Minimal styles - rely on frozen positions */\r\n          .border-b-4.border-blue-900 {\r\n            border-bottom: 4px solid #1e3a8a !important;\r\n            page-break-inside: avoid;\r\n            display: table-header-group;\r\n          }\r\n\r\n          .report-header {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .component-header {\r\n            display: flex !important;\r\n            justify-content: space-between !important;\r\n            align-items: center !important;\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .metrics-flex{\r\n            display: flex !important;\r\n            justify-content: space-between !important;\r\n            align-items: center !important;\r\n          }\r\n\r\n          .metrics-flex > * {\r\n            flex: 1 1 0% !important;\r\n            text-align: center;\r\n          }\r\n\r\n          .component-content {\r\n            display: table-row-group;\r\n          }\r\n\r\n          .min-h-screen {\r\n            display: table !important;\r\n            width: 100% !important;\r\n            min-height: auto !important;\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          // .min-h-screen:not(:second-child) {\r\n          //   page-break-before: always;\r\n          // }\r\n\r\n          /* Trust the frozen chart and legend positions */\r\n          div[id*=\"apex\"] {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .apexcharts-legend {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .bg-gray-200 {\r\n            background-color: #ffffff !important;\r\n          }\r\n\r\n          .bg-white {\r\n            background-color: #ffffff !important;\r\n          }\r\n\r\n          .overflow-y-auto {\r\n            overflow: visible !important;\r\n          }\r\n\r\n          /* Component-specific page assignments */\r\n          .monthly-component {\r\n            page: monthly-report;\r\n          }\r\n\r\n          .year-to-date-component {\r\n            page: year-to-date-report;\r\n          }\r\n\r\n          .balance-sheet-component {\r\n            page: balance-sheet-report;\r\n          }\r\n\r\n          @media print {\r\n            .monthly-component {\r\n              page: monthly-report;\r\n            }\r\n            \r\n            .year-to-date-component {\r\n              page: year-to-date-report;\r\n            }\r\n            \r\n            .balance-sheet-component {\r\n              page: balance-sheet-report;\r\n            }\r\n          }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"report-container\">\r\n          ${clonedContent.innerHTML}\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n\r\n      // console.log(\"Html content prepared for PDF generation with frozen charts and legends\", htmlContent);\r\n\r\n      // Generate PDF using the HTML content\r\n      const { generatePDFFromHTML } = await import('../../services/pdf');\r\n      const response = await generatePDFFromHTML(htmlContent, {\r\n        format: 'A4',\r\n        orientation: 'portrait',\r\n        printBackground: true,\r\n        margin: {\r\n          top: '0in',\r\n          right: '0in',\r\n          bottom: '0.5in',\r\n          left: '0in'\r\n        },\r\n        preferCSSPageSize: true  // Enable CSS @page rules for mixed orientations\r\n      });\r\n\r\n      if (response.success && response.data.pdf) {\r\n        const filename = `Custom_Report_${new Date().toISOString().split('T')[0]}.pdf`;\r\n        const downloadSuccess = downloadPDFFromBase64(response.data.pdf, filename);\r\n\r\n        if (downloadSuccess) {\r\n          setSuccessMessage('PDF downloaded successfully!');\r\n          setShowSuccess(true);\r\n        } else {\r\n          throw new Error('Failed to download PDF');\r\n        }\r\n      } else {\r\n        throw new Error(response.message || 'Failed to generate PDF');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('PDF Generation Error:', error);\r\n      setSuccessMessage('Failed to generate PDF. Please try again.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsGeneratingPDF(false);\r\n    }\r\n  };\r\n\r\n  // Initialize data on component mount\r\n  useEffect(() => {\r\n    // Ensure ApexCharts is available globally for export functionality\r\n    if (!window.ApexCharts) {\r\n      window.ApexCharts = ApexCharts;\r\n    }\r\n\r\n    initializeSettings();\r\n    initializeDataWithConnectionCheck();\r\n    fetchContentSettings(); // NEW: Fetch content settings\r\n  }, [companyId, reportId]); // Add companyId and reportId as dependencies\r\n\r\n  // Auto-redirect timer effect for disconnected state\r\n  useEffect(() => {\r\n    // Start timer when showing disconnected state (dataError exists and not connected)\r\n    if (dataError && qboConnectionStatus && qboConnectionStatus.connectionStatus !== 'CONNECTED' && !isCheckingConnection) {\r\n      startRedirectTimer();\r\n    } else {\r\n      // Clear timer when not in disconnected state\r\n      clearRedirectTimer();\r\n    }\r\n\r\n    // Cleanup timer on unmount\r\n    return () => {\r\n      clearRedirectTimer();\r\n    };\r\n  }, [dataError, qboConnectionStatus, isCheckingConnection]);\r\n\r\n  // New function to check connection status first, then fetch data\r\n  const initializeDataWithConnectionCheck = async () => {\r\n    try {\r\n      // First check QBO connection status\r\n      const connectionStatus = await checkQBOConnectionStatusLocal();\r\n\r\n      // Only fetch report data if connected\r\n      if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\r\n        await fetchReportData();\r\n      } else {\r\n        // If disconnected, set appropriate state\r\n        setIsLoadingData(false);         \r\n        setDataError('Company not connected to QuickBooks');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during initialization:', error);\r\n      setIsLoadingData(false);\r\n      setDataError('Failed to initialize report');\r\n    }\r\n  };\r\n\r\n  const handleBackToDashboard = () => {\r\n    navigate(`/company/${companyId}`, {\r\n      state: { activeTab: 'reports' }\r\n    });\r\n  };\r\n\r\n  const handleSettingChange = (section, property, value) => {\r\n    // Apply font size constraints\r\n    if (property === 'fontSize') {\r\n      const constraints = fontSizeConstraints[section];\r\n      if (constraints) {\r\n        value = Math.max(constraints.min, Math.min(constraints.max, value));\r\n      }\r\n    }\r\n\r\n    setTemplateSettings(prev => ({\r\n      ...prev,\r\n      [section]: {\r\n        ...prev[section],\r\n        [property]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const handleCloseSuccess = () => {\r\n    setShowSuccess(false);\r\n  };\r\n\r\n  // Save settings to API\r\n  const saveSettingsToAPI = async () => {\r\n    try {\r\n      setIsSavingSettings(true); // Keep this for save button\r\n\r\n      const response = await updateTemplateSettings(templateSettings);\r\n\r\n      if (response.data && response.data.success) {\r\n        localStorage.setItem('templateSettings', JSON.stringify(templateSettings));\r\n        setInitialSettings(templateSettings);\r\n        setSuccessMessage('Settings saved successfully and applied!');\r\n        setShowSuccess(true);\r\n      } else {\r\n        throw new Error('Failed to save template settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving template settings:', error);\r\n      setSuccessMessage('Failed to save settings. Please try again.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsSavingSettings(false); // Keep this for save button\r\n    }\r\n  };\r\n\r\n  // Button handlers\r\n  const handleSave = () => {\r\n    setShowSaveConfirmModal(true);\r\n  };\r\n\r\n  const handleConfirmSave = async () => {\r\n    setShowSaveConfirmModal(false);\r\n    await saveSettingsToAPI();\r\n  };\r\n\r\n  const handleCancelSave = () => {\r\n    setShowSaveConfirmModal(false);\r\n  };\r\n\r\n  const handleResetToDefault = () => {\r\n    setShowResetConfirmModal(true);\r\n  };\r\n\r\n  const handleRedirectToConnection = () => {\r\n    // Clear any existing timer when manually redirecting\r\n    clearRedirectTimer();\r\n    navigate(`/company/${companyId}`);\r\n  };\r\n\r\n  // Auto-redirect timer functions\r\n  const startRedirectTimer = () => {\r\n    // Clear any existing timer first\r\n    clearRedirectTimer();\r\n\r\n    // Set initial countdown\r\n    setRedirectCountdown(10);\r\n\r\n    // Create interval that decrements countdown every second\r\n    const timerId = setInterval(() => {\r\n      setRedirectCountdown(prev => {\r\n        if (prev <= 1) {\r\n          // Time's up, redirect and clear timer\r\n          clearInterval(timerId);\r\n          setRedirectTimerRef(null);\r\n          navigate(`/company/${companyId}`);\r\n          return 0;\r\n        }\r\n        return prev - 1;\r\n      });\r\n    }, 1000);\r\n\r\n    // Store timer reference for cleanup\r\n    setRedirectTimerRef(timerId);\r\n  };\r\n\r\n  const clearRedirectTimer = () => {\r\n    if (redirectTimerRef) {\r\n      clearInterval(redirectTimerRef);\r\n      setRedirectTimerRef(null);\r\n    }\r\n    setRedirectCountdown(null);\r\n  };\r\n\r\n  const handleConfirmReset = async () => {\r\n    try {\r\n      setIsResettingSettings(true); // Use new state for reset button\r\n\r\n      let resetSettings = defaultSettings; // Default fallback\r\n      let resetMessage = 'Settings reset to default values and saved successfully!';\r\n\r\n      // Determine reset behavior based on user role\r\n      if (currentUser) {\r\n        if (currentUser.isAdmin) {\r\n          // Admin users: Reset to hardcoded default settings\r\n          resetSettings = defaultSettings;\r\n          resetMessage = 'Settings reset to system default values and saved successfully!';\r\n        } else {\r\n          // Normal users: Reset to global settings from database\r\n          try {\r\n            const globalResponse = await getGlobalSettings('DEEPSIGHT');\r\n            if (globalResponse.data && globalResponse.data.success) {\r\n              resetSettings = globalResponse.data.data.settings;\r\n              resetMessage = 'Settings reset to global default values and saved successfully!';\r\n            } else {\r\n              console.warn('Failed to fetch global settings, using hardcoded defaults');\r\n            }\r\n          } catch (globalError) {\r\n            console.error('Error fetching global settings:', globalError);\r\n            console.warn('Using hardcoded defaults as fallback');\r\n          }\r\n        }\r\n      }\r\n\r\n      // Apply the reset settings\r\n      setTemplateSettings(resetSettings);\r\n\r\n      // Save the reset settings to the server\r\n      const response = await updateTemplateSettings(resetSettings);\r\n\r\n      if (response.data && response.data.success) {\r\n        localStorage.setItem('templateSettings', JSON.stringify(resetSettings));\r\n        setInitialSettings(resetSettings);\r\n        setSuccessMessage(resetMessage);\r\n        setShowSuccess(true);\r\n      } else {\r\n        throw new Error('Failed to save reset template settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error resetting and saving settings:', error);\r\n      // Fallback to default settings if everything fails\r\n      setTemplateSettings(defaultSettings);\r\n      setInitialSettings(defaultSettings);\r\n      localStorage.setItem('templateSettings', JSON.stringify(defaultSettings));\r\n      setSuccessMessage('Settings reset to default but failed to save to server. Please try saving manually.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsResettingSettings(false); // Use new state for reset button\r\n      setShowResetConfirmModal(false);\r\n    }\r\n  };\r\n\r\n  const handleCancelReset = () => {\r\n    setShowResetConfirmModal(false);\r\n  };\r\n\r\n\r\n  const handleResync = async () => {\r\n    try {\r\n      // Check connection status first\r\n      const connectionStatus = await checkQBOConnectionStatusLocal();\r\n\r\n      if (connectionStatus && connectionStatus.connectionStatus === 'CONNECTED') {\r\n        await fetchReportData();\r\n        fetchContentSettings(); // Also resync content settings\r\n        setSuccessMessage('Data resynced successfully!');\r\n      } else {\r\n        setSuccessMessage('Cannot resync: Company not connected to QuickBooks.');\r\n      }\r\n      setShowSuccess(true);\r\n    } catch (error) {\r\n      console.error('Error during resync:', error);\r\n      setSuccessMessage('Failed to resync data. Please try again.');\r\n      setShowSuccess(true);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Convert font type to CSS font-weight\r\n  const getFontWeight = (fontType) => {\r\n    const weights = {\r\n      'Regular': '400',\r\n      'Bold': '700'\r\n    };\r\n    return weights[fontType] || '400';\r\n  };\r\n\r\n  const getHeaderStyle = () => ({\r\n    fontFamily: templateSettings?.header?.fontStyle || 'Open Sans',\r\n    fontWeight: getFontWeight(templateSettings?.header?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.header?.fontSize || 44}px`,\r\n    color: templateSettings?.header?.color || '#1e7c8c',\r\n    borderRadius: '8px 8px 0 0',\r\n    margin: '0',\r\n  });\r\n\r\n  const getHeadingStyle = () => ({\r\n    fontFamily: templateSettings?.heading?.fontStyle || 'Open Sans',\r\n    fontWeight: getFontWeight(templateSettings?.heading?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.heading?.fontSize || 36}px`,\r\n    color: templateSettings?.heading?.color || '#1e7c8c',\r\n  });\r\n\r\n  const getH2Style = () => ({\r\n    fontFamily : templateSettings?.h2?.fontStyle || 'Open Sans',\r\n    fontWeight : getFontWeight(templateSettings?.h2?.fontType || 'Bold'),\r\n    fontSize : `${templateSettings?.h2?.fontSize || 24}px`,\r\n    color : templateSettings?.h2?.color || '#333333'\r\n  });\r\n\r\n  const getH3Style = () => ({\r\n    fontFamily : templateSettings?.h3?.fontStyle || 'Open Sans',\r\n    fontWeight : getFontWeight(templateSettings?.h3?.fontType || 'Bold'),\r\n    fontSize : `${templateSettings?.h3?.fontSize || 18}px`,\r\n    color : templateSettings?.h3?.color || '#333333' \r\n  });\r\n\r\n  const getContentStyle = () => ({\r\n    fontFamily: templateSettings?.content?.fontStyle || 'Open Sans',\r\n    fontWeight: getFontWeight(templateSettings?.content?.fontType || 'Regular'),\r\n    fontSize: `${templateSettings?.content?.fontSize || 15}px`,\r\n    color: templateSettings?.content?.color || '#333333',\r\n    lineHeight: '1.6',\r\n    margin: '0'\r\n  });\r\n\r\n  const getSubHeadingStyle = () => ({\r\n    fontFamily: templateSettings?.subHeading?.fontStyle || 'Helvetica',\r\n    fontWeight: getFontWeight(templateSettings?.subHeading?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.subHeading?.fontSize || 22}px`,\r\n    color: templateSettings?.subHeading?.color || '#1e7c8c',\r\n    padding: '0'\r\n  });\r\n\r\n  // Show loading state while fetching initial settings, content settings, or user info\r\n  if (isLoadingSettings || isLoadingContentSettings || isLoadingUser || !templateSettings) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"text-center\">\r\n          <CircularProgress size={40} />\r\n          <div className=\"mt-4 text-lg text-gray-600\">Loading settings...</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show loading state while checking connection\r\n  if (isCheckingConnection && qboConnectionStatus === null) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"text-center\">\r\n          <CircularProgress size={40} />\r\n          <div className=\"mt-4 text-lg text-gray-600\">Checking QuickBooks connection...</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Check if company is connected to QBO\r\n  const isConnected = qboConnectionStatus && qboConnectionStatus.connectionStatus === 'CONNECTED';\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Snackbar for success/error messages */}\r\n      <Snackbar\r\n        open={showSuccess}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSuccess}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSuccess}\r\n          severity={dataError || settingsError || contentSettingsError ? \"error\" : \"success\"}\r\n          variant=\"filled\"\r\n          sx={{\r\n            backgroundColor: dataError || settingsError || contentSettingsError ? '#d32f2f' : '#1976d2',\r\n            '& .MuiAlert-icon': {\r\n              color: 'white',\r\n            },\r\n          }}\r\n        >\r\n          {successMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n\r\n      {/* Save Confirmation Modal */}\r\n      <Dialog\r\n        open={showSaveConfirmModal}\r\n        onClose={handleCancelSave}\r\n        aria-labelledby=\"save-dialog-title\"\r\n        aria-describedby=\"save-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"save-dialog-title\">\r\n          Confirm Save Settings\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"save-dialog-description\">\r\n            Are you sure you want to save these template settings? These settings will be applied to all future reports.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCancelSave} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleConfirmSave}\r\n            color=\"primary\"\r\n            variant=\"contained\"\r\n            disabled={isSavingSettings}\r\n          >\r\n            {isSavingSettings ? (\r\n              <>\r\n                <CircularProgress size={16} sx={{ mr: 1 }} />\r\n                Saving...\r\n              </>\r\n            ) : (\r\n              'Save Settings'\r\n            )}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Reset Confirmation Modal */}\r\n      <Dialog\r\n        open={showResetConfirmModal}\r\n        onClose={handleCancelReset}\r\n        aria-labelledby=\"reset-dialog-title\"\r\n        aria-describedby=\"reset-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"reset-dialog-title\">\r\n          Confirm Reset Settings\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"reset-dialog-description\">\r\n            Are you sure you want to reset the template settings? Any unsaved changes will be lost.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCancelReset} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleConfirmReset}\r\n            color=\"primary\"\r\n            variant=\"contained\"\r\n            disabled={isResettingSettings} // Use new state instead of isSavingSettings\r\n          >\r\n            {isResettingSettings ? (\r\n              <>\r\n                <CircularProgress size={16} sx={{ mr: 1 }} />\r\n                Resetting...\r\n              </>\r\n            ) : (\r\n              'Reset Settings'\r\n            )}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Fixed Sidebar - Full Height - Only show when connected */}\r\n      {isConnected && (\r\n        <div className=\"w-96 flex-shrink-0 h-screen flex flex-col fixed left-0 top-0 z-10 bg-gray-50\">\r\n          <div className=\"px-4 py-[14px] border-b bg-gray-50 flex items-center shadow\">\r\n            <Tooltip title=\"Back to Reports\" placement=\"bottom\">\r\n              <IconButton\r\n                onClick={handleBackToDashboard}\r\n                sx={{\r\n                  color: 'rgb(75, 85, 99)',\r\n                  padding: '6px',\r\n                  marginRight: '12px',\r\n                  '&:hover': {\r\n                    backgroundColor: 'rgba(75, 85, 99, 0.1)',\r\n                  },\r\n                  '&:focus': {\r\n                    outline: 'none',\r\n                  },\r\n                  transition: 'all 0.2s',\r\n                }}\r\n              >\r\n                <ArrowBack fontSize=\"medium\" />\r\n              </IconButton>\r\n            </Tooltip>\r\n            {/* <h2 className=\"text-lg font-semibold text-gray-900\">Customize Template</h2> */}\r\n          </div>\r\n\r\n        <div className=\"p-4 space-y-6 overflow-y-auto flex-1 shadow\">\r\n          {/* Header Section */}\r\n          <h2 className=\"text-lg font-semibold text-gray-900 font-roboto\">Customize Template</h2>\r\n          {/* Header Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">Header</h3>\r\n              <Tooltip\r\n                title=\"Customize the main report header — typically used for your company name, report title, or client details. Adjust font, color, and size to match your branding.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.header.fontStyle}\r\n                  onChange={(e) => handleSettingChange('header', 'fontStyle', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.header.fontType}\r\n                    onChange={(e) => handleSettingChange('header', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.header.min}-{fontSizeConstraints.header.max})\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.header.min,\r\n                    max: fontSizeConstraints.header.max,\r\n                  }}\r\n                  value={templateSettings.header.fontSize}\r\n                  onChange={(e) => handleSettingChange('header', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.header.color}\r\n                  onChange={(e) => handleSettingChange('header', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Heading Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">Heading</h3>\r\n              <Tooltip\r\n                title=\"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.heading.fontStyle}\r\n                  onChange={(e) => handleSettingChange('heading', 'fontStyle', e.target.value)}\r\n                   sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.heading.fontType}\r\n                    onChange={(e) => handleSettingChange('heading', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.heading.min}-{fontSizeConstraints.heading.max})\r\n                </InputLabel>\r\n                <TextField fullWidth  \r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.heading.min,\r\n                    max: fontSizeConstraints.heading.max,\r\n                  }}\r\n                  value={templateSettings.heading.fontSize}\r\n                  onChange={(e) => handleSettingChange('heading', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.heading.color}\r\n                  onChange={(e) => handleSettingChange('heading', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* H2 Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">H2</h3>\r\n              <Tooltip\r\n                title=\"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.h2.fontStyle}\r\n                  onChange={(e) => handleSettingChange('h2', 'fontStyle', e.target.value)}\r\n                   sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.h2.fontType}\r\n                    onChange={(e) => handleSettingChange('h2', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.h2.min}-{fontSizeConstraints.h2.max})\r\n                </InputLabel>\r\n                <TextField fullWidth  \r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.h2.min,\r\n                    max: fontSizeConstraints.h2.max,\r\n                  }}\r\n                  value={templateSettings.h2.fontSize}\r\n                  onChange={(e) => handleSettingChange('h2', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.h2.color}\r\n                  onChange={(e) => handleSettingChange('h2', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n           {/* H3 Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">H3</h3>\r\n              <Tooltip\r\n                title=\"Control the formatting of section headings, such as Income Summary, Net Income, or Balance Sheet. Helps keep your report organized and easy to read.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.h3.fontStyle}\r\n                  onChange={(e) => handleSettingChange('h3', 'fontStyle', e.target.value)}\r\n                   sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.h3.fontType}\r\n                    onChange={(e) => handleSettingChange('h3', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.h3.min}-{fontSizeConstraints.h3.max})\r\n                </InputLabel>\r\n                <TextField fullWidth  \r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.h3.min,\r\n                    max: fontSizeConstraints.h3.max,\r\n                  }}\r\n                  value={templateSettings.h3.fontSize}\r\n                  onChange={(e) => handleSettingChange('h3', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.h3.color}\r\n                  onChange={(e) => handleSettingChange('h3', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Sub-Heading Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">Sub-Heading</h3>\r\n              <Tooltip\r\n                title=\"Style sub-headings that appear under main sections, for example 'Q1 Performance' under Income Summary. Use this to make detailed sections stand out.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.subHeading.fontStyle}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'fontStyle', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.subHeading.fontType}\r\n                    onChange={(e) => handleSettingChange('subHeading', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.subHeading.min}-{fontSizeConstraints.subHeading.max})\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.subHeading.min,\r\n                    max: fontSizeConstraints.subHeading.max,\r\n                  }}\r\n                  value={templateSettings.subHeading.fontSize}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.subHeading.color}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          {/* Content Section */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-3\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 font-roboto\">Content</h3>\r\n              <Tooltip\r\n                title=\"Format the main body text of your report, including descriptions, analysis, and explanatory content. This affects paragraph text and detailed information throughout the report.\"\r\n                placement=\"right\"\r\n                arrow\r\n              >\r\n                <InfoIcon\r\n                  fontSize=\"small\"\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                  sx={{ fontSize: '17px' }}\r\n                />\r\n              </Tooltip>\r\n            </div>\r\n\r\n            <div className=\"mb-3\">\r\n              <InputLabel\r\n                sx={{\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  color: 'rgb(75, 85, 99)',\r\n                  marginBottom: '4px',\r\n                }}\r\n              >\r\n                Font Style\r\n              </InputLabel>\r\n              <FormControl fullWidth>\r\n                <Select\r\n                  value={templateSettings.content.fontStyle}\r\n                  onChange={(e) => handleSettingChange('content', 'fontStyle', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiSelect-select': {\r\n                      fontSize: '0.875rem',\r\n                      padding: '8px 12px',\r\n                      fontWeight: 400,\r\n                    },\r\n                    '& .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: 'rgb(209, 213, 219)',\r\n                    },\r\n                    '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                      borderColor: '#3b82f6',\r\n                    },\r\n                  }}\r\n                >\r\n                  {fontStyles.map(font => (\r\n                    <MenuItem key={font} value={font}>{font}</MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Type\r\n                </InputLabel>\r\n                <FormControl fullWidth>\r\n                  <Select\r\n                    value={templateSettings.content.fontType}\r\n                    onChange={(e) => handleSettingChange('content', 'fontType', e.target.value)}\r\n                    sx={{\r\n                      '& .MuiSelect-select': {\r\n                        fontSize: '0.875rem',\r\n                        padding: '8.7px 8px',\r\n                        fontWeight: 400,\r\n                      },\r\n                      '& .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    }}\r\n                  >\r\n                    {fontTypes.map(type => (\r\n                      <MenuItem key={type} value={type}>{type}</MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Font Size ({fontSizeConstraints.content.min}-{fontSizeConstraints.content.max})\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"number\"\r\n                  inputProps={{\r\n                    min: fontSizeConstraints.content.min,\r\n                    max: fontSizeConstraints.content.max,\r\n                  }}\r\n                  value={templateSettings.content.fontSize}\r\n                  onChange={(e) => handleSettingChange('content', 'fontSize', parseInt(e.target.value))}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      fontSize: '0.875rem',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '10px 8px',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <InputLabel\r\n                  sx={{\r\n                    fontSize: '0.75rem',\r\n                    fontWeight: 500,\r\n                    color: 'rgb(75, 85, 99)',\r\n                    marginBottom: '4px',\r\n                  }}\r\n                >\r\n                  Color\r\n                </InputLabel>\r\n                <TextField fullWidth\r\n                  type=\"color\"\r\n                  value={templateSettings.content.color}\r\n                  onChange={(e) => handleSettingChange('content', 'color', e.target.value)}\r\n                  sx={{\r\n                    '& .MuiOutlinedInput-root': {\r\n                      height: '40px',\r\n                      '& fieldset': {\r\n                        borderColor: 'rgb(209, 213, 219)',\r\n                      },\r\n                      '&:hover fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#3b82f6',\r\n                      },\r\n                    },\r\n                    '& .MuiOutlinedInput-input': {\r\n                      padding: '0',\r\n                      height: '36px',\r\n                      cursor: 'pointer',\r\n                    },\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"pt-7 border-t border-gray-300\">\r\n            <div className=\"flex flex-row gap-4\">\r\n              <button\r\n                onClick={handleResetToDefault}\r\n                disabled={isResettingSettings} // Use new state for reset button\r\n                className=\"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-neutral-800\"\r\n              >\r\n                {isResettingSettings ? (\r\n                  <>\r\n                    <CircularProgress size={14} sx={{ mr: 1 }} />\r\n                    RESETTING...\r\n                  </>\r\n                ) : (\r\n                  'RESET'\r\n                )}\r\n              </button>\r\n\r\n              <button\r\n                onClick={handleSave}\r\n                disabled={isSavingSettings} // Keep this for save button\r\n                className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-neutral-800\"\r\n              >\r\n                {isSavingSettings ? (\r\n                  <>\r\n                    <CircularProgress size={14} sx={{ mr: 1 }} />\r\n                    SAVING...\r\n                  </>\r\n                ) : (\r\n                  'SAVE'\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      )}\r\n\r\n      {/* Main Content Area - Header and Content */}\r\n      <div className=\"flex flex-col flex-1 h-screen\" style={{ marginLeft: isConnected ? '384px' : '0' }}>\r\n        {/* Fixed Header - Only show when connected */}\r\n        {isConnected && (\r\n          <div className=\"px-6 py-3 flex items-center bg-gray-50 justify-end fixed top-0 z-5 shadow\"\r\n            style={{ left: isConnected ? '384px' : '0', right: '0' }}>\r\n\r\n            <div className=\"flex items-center justify-end space-x-3\">\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                <Tooltip title={isLoadingData ? \"Loading data...\" : \"Resync data\"} placement=\"top\">\r\n                  <span>\r\n                    <IconButton\r\n                      onClick={handleResync}\r\n                      disabled={isLoadingData}\r\n                      sx={{\r\n                        color: isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\r\n                        '&:hover': {\r\n                          backgroundColor: 'rgba(156, 163, 175, 0.1)',\r\n                        },\r\n                        '&:focus': {\r\n                          outline: 'none',\r\n                        },\r\n                        '&:disabled': {\r\n                          color: 'rgba(156, 163, 175, 0.6)',\r\n                          cursor: 'not-allowed',\r\n                        },\r\n                        transition: 'all 0.2s',\r\n                        padding: '8px',\r\n                      }}\r\n                    >\r\n                      {isLoadingData ? (\r\n                        <CircularProgress size={16} color=\"inherit\" />\r\n                      ) : (\r\n                        <SyncIcon fontSize=\"medium\" />\r\n                      )}\r\n                    </IconButton>\r\n                  </span>\r\n                </Tooltip>\r\n\r\n                <Tooltip title={isGeneratingPDF ? \"Generating PDF...\" : \"Download PDF (Charts upload in background)\"} placement=\"top\">\r\n                  <span>\r\n                    <IconButton\r\n                      onClick={handleDownloadPDF}\r\n                      disabled={isGeneratingPDF || isLoadingData}\r\n                      sx={{\r\n                        color: (isGeneratingPDF || isLoadingData) ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\r\n                        '&:hover': {\r\n                          backgroundColor: 'rgba(156, 163, 175, 0.1)',\r\n                        },\r\n                        '&:focus': {\r\n                          outline: 'none',\r\n                        },\r\n                        '&:disabled': {\r\n                          color: 'rgba(156, 163, 175, 0.6)',\r\n                          cursor: 'not-allowed',\r\n                        },\r\n                        transition: 'all 0.2s',\r\n                        padding: '8px',\r\n                      }}\r\n                    >\r\n                      {isGeneratingPDF ? (\r\n                        <CircularProgress size={16} color=\"inherit\" />\r\n                      ) : (\r\n                        <DownloadIcon fontSize=\"medium\" />\r\n                      )}\r\n                    </IconButton>\r\n                  </span>\r\n                </Tooltip>\r\n              </Box>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Scrollable Content Panel */}\r\n        <div\r\n          className=\"flex-1 overflow-y-auto bg-gray-200\"\r\n          style={{ marginTop: isConnected ? '60px' : '0px', width: '100%' }}\r\n        >\r\n          {/* Back button for disconnected state */}\r\n          {!isConnected && (\r\n            <div className=\"p-4 bg-white shadow-sm\">\r\n              <Tooltip title=\"Back to Reports\" placement=\"bottom\">\r\n                <IconButton\r\n                  onClick={handleBackToDashboard}\r\n                  sx={{\r\n                    color: 'rgb(75, 85, 99)',\r\n                    padding: '6px',\r\n                    '&:hover': {\r\n                      backgroundColor: 'rgba(75, 85, 99, 0.1)',\r\n                    },\r\n                    '&:focus': {\r\n                      outline: 'none',\r\n                    },\r\n                    transition: 'all 0.2s',\r\n                  }}\r\n                >\r\n                  <ArrowBack fontSize=\"medium\" />\r\n                </IconButton>\r\n              </Tooltip>\r\n            </div>\r\n          )}\r\n\r\n          {isLoadingData ? (\r\n  <div className=\"flex items-center justify-center h-64\">\r\n    <div className=\"text-center\">\r\n      <CircularProgress size={40} />\r\n      <div className=\"mt-4 text-lg text-gray-600\">Loading report data...</div>\r\n    </div>\r\n  </div>\r\n) : dataError ? (\r\n  <div className=\"flex items-center justify-center h-64\">\r\n    <div className=\"text-center\">\r\n      {isCheckingConnection ? (\r\n        <>\r\n          <CircularProgress size={40} />\r\n          <div className=\"mt-4 text-lg text-gray-600\">Checking connection status...</div>\r\n        </>\r\n      ) : qboConnectionStatus && qboConnectionStatus.connectionStatus !== 'CONNECTED' ? (\r\n        <>\r\n          <div className=\"text-lg text-red-600 mb-2\">Company Disconnected from QuickBooks</div>\r\n          <div className=\"text-gray-600 mb-3\">\r\n            Please connect your company to QuickBooks to generate reports.\r\n          </div>\r\n\r\n          {/* Auto-redirect countdown message */}\r\n          {redirectCountdown !== null && (\r\n            <div className=\"text-sm text-gray-500 mb-3\">\r\n              Redirecting you to company connection page in <span className=\"font-semibold text-blue-600\">{redirectCountdown}</span> seconds\r\n            </div>\r\n          )}\r\n\r\n          <button\r\n            onClick={handleRedirectToConnection}\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-sm font-medium\"\r\n          >\r\n            GO TO COMPANY INFO\r\n          </button>\r\n        </>\r\n      ) : (\r\n        <>\r\n          <div className=\"text-lg text-red-600 mb-4\">Failed to load report data</div>\r\n          <button\r\n            onClick={fetchReportData}\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </>\r\n      )}\r\n    </div>\r\n  </div>\r\n) : isConnected ? (\r\n  <>\r\n\r\n    {/* Show report components only when connected and data is loaded */}\r\n    <DeepSightCoverPage\r\n      reportData={reportData}\r\n    />\r\n\r\n    <TableOfContents\r\n      headingTextStyle={getHeadingStyle()}\r\n      headerTextStyle={getHeaderStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <ReportSummary\r\n      headerTextStyle={getHeaderStyle()}\r\n      headingTextStyle={getHeadingStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      h2TextStyle={getH2Style()}\r\n      h3TextStyle={getH3Style()}\r\n      reportData={reportData}\r\n    />\r\n    <FiscalYearDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      fiscalData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <ExpenseSummaryDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <OperationalEfficiencyDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      operationalData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <LiquiditySummaryDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      liquidityData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <ProfitLoss13MonthDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <ProfitLossMonthlyDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <ProfitLossYTDDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n    <BalanceSheetDashboard\r\n      headerTextStyle={getHeaderStyle()}\r\n      subHeadingTextStyle={getSubHeadingStyle()}\r\n      contentTextStyle={getContentStyle()}\r\n      reportData={reportData}\r\n      contentSettings={contentSettings}\r\n    />\r\n  </>\r\n          ) : null}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomizeTemplateWithPreview;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,eAAe;AAAE,SAASC,IAAI,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,IAAIC,YAAY,QAAQ,qBAAqB;AAC3S,SAASC,QAAQ,EAAEC,KAAK,QAAQ,eAAe;AAC/C,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,uBAAuB,MAAM,8BAA8B;AAClE,OAAOC,8BAA8B,MAAM,qCAAqC;AAChF,OAAOC,yBAAyB,MAAM,gCAAgC;AACtE,OAAOC,0BAA0B,MAAM,6BAA6B;AACpE,OAAOC,0BAA0B,MAAM,uBAAuB;AAC9D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SACEC,kBAAkB,EAClBC,wBAAwB,EACxBC,mBAAmB,EACnBC,sBAAsB,EACtBC,yBAAyB,EACzBC,gBAAgB,EAChBC,iBAAiB,QACZ,uCAAuC;AAC9C,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,IAAIC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAK/D,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,CAAC,GAAG/B,eAAe,CAAC,CAAC;EAExC,MAAMgC,MAAM,GAAGpB,SAAS,CAAC,CAAC;EAC1B,MAAMqB,SAAS,GAAGD,MAAM,CAACE,EAAE;EAC3B,MAAMC,QAAQ,GAAGH,MAAM,CAACG,QAAQ;;EAEhC;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAACkF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACgG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACkG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACF,MAAM,CAACoG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACwG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC0G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM4G,QAAQ,GAAGzE,WAAW,CAAC,CAAC;;EAE9B;;EAEA;EACA,MAAM0E,eAAe,GAAG;IACtBC,MAAM,EAAE;MACNC,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPJ,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDE,EAAE,EAAG;MACHL,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAG,MAAM;MACjBC,QAAQ,EAAG,EAAE;MACbC,KAAK,EAAG;IACV,CAAC;IACDG,EAAE,EAAG;MACHN,SAAS,EAAG,WAAW;MACvBC,QAAQ,EAAG,MAAM;MACjBC,QAAQ,EAAG,EAAE;MACbC,KAAK,EAAG;IACV,CAAC;IACDI,UAAU,EAAE;MACVP,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDK,OAAO,EAAE;MACPR,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT;EACF,CAAC;EAED,MAAMM,UAAU,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;EAC1G,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;;EAErC;EACA,MAAMC,mBAAmB,GAAG;IAC1BZ,MAAM,EAAE;MAAEa,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAC5BT,OAAO,EAAE;MAAEQ,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAC7BR,EAAE,EAAG;MAACO,GAAG,EAAG,EAAE;MAAEC,GAAG,EAAG;IAAG,CAAC;IAC1BP,EAAE,EAAG;MAACM,GAAG,EAAG,EAAE;MAAEC,GAAG,EAAG;IAAE,CAAC;IACzBN,UAAU,EAAE;MAAEK,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAChCL,OAAO,EAAE;MAAEI,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF5C,2BAA2B,CAAC,IAAI,CAAC;MACjCE,uBAAuB,CAAC,IAAI,CAAC;MAE7B,MAAM2C,QAAQ,GAAG,MAAMxF,kBAAkB,CAACmB,SAAS,EAAE,WAAW,CAAC;MAEjE,IAAIqE,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,YAAY,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI;QACvChD,kBAAkB,CAACkD,YAAY,CAAC;MAClC,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhD,uBAAuB,CAACgD,KAAK,CAACE,OAAO,CAAC;;MAEtC;MACA,MAAMC,sBAAsB,GAAG;QAC7BC,aAAa,EAAE;UACbC,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,iBAAiB,EAAE,IAAI;UACvBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE;QACb;MACF,CAAC;MACD7D,kBAAkB,CAACuD,sBAAsB,CAAC;MAE1C/C,iBAAiB,CAAC,2DAA2D,CAAC;MAC9EF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRJ,2BAA2B,CAAC,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAM4D,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAClD,IAAI;MACFtC,uBAAuB,CAAC,IAAI,CAAC;MAC7B,MAAMuB,QAAQ,GAAG,MAAMvF,wBAAwB,CAACkB,SAAS,CAAC;MAE1D,IAAIqE,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QAC1C3B,sBAAsB,CAACyB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC;QAC1C,OAAOD,QAAQ,CAACC,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,uCAAuC,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D;MACA9B,sBAAsB,CAAC;QAAEyC,gBAAgB,EAAE;MAAe,CAAC,CAAC;MAC5D,OAAO;QAAEA,gBAAgB,EAAE;MAAe,CAAC;IAC7C,CAAC,SAAS;MACRvC,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAEC;EACA,MAAMwC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFxE,oBAAoB,CAAC,IAAI,CAAC;MAC1BM,gBAAgB,CAAC,IAAI,CAAC;MACtBJ,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAMqD,QAAQ,GAAG,MAAMtF,mBAAmB,CAAC,CAAC;MAE5C,IAAIsF,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,YAAY,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACiB,QAAQ;QAChD,MAAMC,QAAQ,GAAGnB,QAAQ,CAACC,IAAI,CAACmB,IAAI;;QAEnC;QACAC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACrB,YAAY,CAAC,CAAC;;QAEtE;QACA9D,mBAAmB,CAAC8D,YAAY,CAAC;QACjC5D,kBAAkB,CAAC4D,YAAY,CAAC;;QAEhC;QACA,IAAIgB,QAAQ,EAAE;UACZtE,cAAc,CAACsE,QAAQ,CAAC;QAC1B;MACF,CAAC,MAAM;QACL,MAAM,IAAIf,KAAK,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD1D,gBAAgB,CAAC0D,KAAK,CAACE,OAAO,CAAC;;MAE/B;MACA,MAAMkB,aAAa,GAAGJ,YAAY,CAACK,OAAO,CAAC,kBAAkB,CAAC;MAC9D,MAAMC,gBAAgB,GAAGF,aAAa,GAAGF,IAAI,CAACK,KAAK,CAACH,aAAa,CAAC,GAAG1C,eAAe;MAEpF1C,mBAAmB,CAACsF,gBAAgB,CAAC;MACrCpF,kBAAkB,CAACoF,gBAAgB,CAAC;MAEpClE,iBAAiB,CAAC,oDAAoD,CAAC;MACvEF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRd,oBAAoB,CAAC,KAAK,CAAC;MAC3BM,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACF;MACA,MAAMC,WAAW,GAAGrG,YAAY,CAACsG,GAAG,CAAC,kBAAkB,CAAC;MACxD,IAAID,WAAW,EAAE;QACf,MAAME,cAAc,GAAGT,IAAI,CAACK,KAAK,CAACE,WAAW,CAAC;QAC9CzF,mBAAmB,CAAC2F,cAAc,CAAC;QACnCzF,kBAAkB,CAACyF,cAAc,CAAC;QAClCvF,oBAAoB,CAAC,KAAK,CAAC;QAC3B;MACF;;MAEA;MACAwE,qBAAqB,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDhE,mBAAmB,CAAC0C,eAAe,CAAC;MACpCxC,kBAAkB,CAACwC,eAAe,CAAC;MACnCtC,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACF,MAAMwF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFhG,gBAAgB,CAAC,IAAI,CAAC;MACtBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM6E,gBAAgB,GAAG,MAAMD,6BAA6B,CAAC,CAAC;;MAE9D;MACA,IAAIC,gBAAgB,IAAIA,gBAAgB,CAACA,gBAAgB,KAAK,WAAW,EAAE;QACzE,MAAMhB,QAAQ,GAAG,MAAMpF,yBAAyB,CAACe,SAAS,EAAEE,QAAQ,CAAC;QAErE,IAAImE,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;UAC1CnE,aAAa,CAACiE,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC;QACnC,CAAC,MAAM;UACL,MAAM,IAAIG,KAAK,CAAC,6BAA6B,CAAC;QAChD;MACF,CAAC,MAAM;QACL;QACAjE,YAAY,CAAC,qCAAqC,CAAC;QACnDsB,iBAAiB,CAAC,6EAA6E,CAAC;QAChGF,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDlE,YAAY,CAACkE,KAAK,CAACE,OAAO,CAAC;MAE3B9C,iBAAiB,CAAC,+CAA+C,CAAC;MAClEF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRtB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EACC;EACA;;EAEA;EACA,MAAMiG,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,OAAO,GAAG,EAAE,KAAK;IACxD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB,IAAI;UACF;UACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;UAEnC;UACAJ,MAAM,CAACK,KAAK,GAAGR,GAAG,CAACQ,KAAK,GAAIZ,OAAO,GAAG,CAAE;UACxCO,MAAM,CAACM,MAAM,GAAGT,GAAG,CAACS,MAAM,GAAIb,OAAO,GAAG,CAAE;;UAE1C;UACAU,GAAG,CAACI,SAAS,GAAG,SAAS;UACzBJ,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAER,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;;UAE/C;UACAH,GAAG,CAACM,WAAW,GAAG,SAAS;UAC3BN,GAAG,CAACO,SAAS,GAAG,CAAC;UACjBP,GAAG,CAACQ,UAAU,CAAClB,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG,CAAC,EAAEI,GAAG,CAACQ,KAAK,GAAG,CAAC,EAAER,GAAG,CAACS,MAAM,GAAG,CAAC,CAAC;;UAEvE;UACAH,GAAG,CAACS,SAAS,CAACf,GAAG,EAAEJ,OAAO,EAAEA,OAAO,CAAC;;UAEpC;UACA,MAAMoB,aAAa,GAAGb,MAAM,CAACc,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;UACzDnB,OAAO,CAACkB,aAAa,CAAC;QACxB,CAAC,CAAC,OAAOnD,KAAK,EAAE;UACdkC,MAAM,CAAClC,KAAK,CAAC;QACf;MACF,CAAC;MAEDmC,GAAG,CAACkB,OAAO,GAAG,MAAM;QAClBnB,MAAM,CAAC,IAAInC,KAAK,CAAC,4BAA4B,CAAC,CAAC;MACjD,CAAC;MAEDoC,GAAG,CAACmB,GAAG,GAAGxB,YAAY;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAG;IACxB;IACAC,WAAW,EAAE;MAAEC,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAkB,CAAC;IAChEC,gBAAgB,EAAE;MAAEF,IAAI,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAqB,CAAC;IAC7EE,oBAAoB,EAAE;MAAEH,IAAI,EAAE,qCAAqC;MAAEC,KAAK,EAAE;IAAiC,CAAC;IAC9GG,iBAAiB,EAAE;MAAEJ,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAyB,CAAC;IAEnF;IACAI,kBAAkB,EAAE;MAAEL,IAAI,EAAE,2BAA2B;MAAEC,KAAK,EAAE;IAAgC,CAAC;IACjGK,cAAc,EAAE;MAAEN,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAmB,CAAC;IACvEM,gBAAgB,EAAE;MAAEP,IAAI,EAAE,2BAA2B;MAAEC,KAAK,EAAE;IAAsB,CAAC;IACrFO,oBAAoB,EAAE;MAAER,IAAI,EAAE,yBAAyB;MAAEC,KAAK,EAAE;IAA0B,CAAC;IAE3F;IACAQ,qBAAqB,EAAE;MAAET,IAAI,EAAE,8BAA8B;MAAEC,KAAK,EAAE;IAA+B,CAAC;IACtGS,wBAAwB,EAAE;MAAEV,IAAI,EAAE,iCAAiC;MAAEC,KAAK,EAAE;IAAiC,CAAC;IAC9GU,yBAAyB,EAAE;MAAEX,IAAI,EAAE,kCAAkC;MAAEC,KAAK,EAAE;IAA6B,CAAC;IAC5GW,mBAAmB,EAAE;MAAEZ,IAAI,EAAE,6BAA6B;MAAEC,KAAK,EAAE;IAAwB,CAAC;IAC5FY,uBAAuB,EAAE;MAAEb,IAAI,EAAE,4BAA4B;MAAEC,KAAK,EAAE;IAA6B,CAAC;IAEpG;IACAa,cAAc,EAAE;MAAEd,IAAI,EAAE,uBAAuB;MAAEC,KAAK,EAAE;IAA2B,CAAC;IACpFc,eAAe,EAAE;MAAEf,IAAI,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAoB,CAAC;IAC1Ee,eAAe,EAAE;MAAEhB,IAAI,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAA4B;EACnF,CAAC;;EAED;EACA,MAAMgB,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACF,MAAMC,KAAK,GAAGC,MAAM,CAACF,QAAQ,CAAC;MAC9B,IAAI,CAACC,KAAK,EAAE;QAAA,IAAAE,qBAAA;QACV,MAAM,IAAI/E,KAAK,CAAC,GAAG,EAAA+E,qBAAA,GAAAvB,iBAAiB,CAACoB,QAAQ,CAAC,cAAAG,qBAAA,uBAA3BA,qBAAA,CAA6BpB,KAAK,KAAIiB,QAAQ,6BAA6B,CAAC;MACjG;;MAEA;MACA,MAAM,IAAI3C,OAAO,CAACC,OAAO,IAAI8C,UAAU,CAAC9C,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,MAAM+C,GAAG,GAAG,MAAMJ,KAAK,CAACK,OAAO,CAAC;QAC9BC,IAAI,EAAE,KAAK;QACXvC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,GAAG;QACXuC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAI,CAACJ,GAAG,IAAI,CAACA,GAAG,CAACK,MAAM,EAAE;QACvB,MAAM,IAAItF,KAAK,CAAC,sCAAsC,CAAC;MACzD;;MAEA;MACA,MAAMuF,kBAAkB,GAAG,MAAMzD,iBAAiB,CAACmD,GAAG,CAACK,MAAM,EAAE,EAAE,CAAC;;MAElE;MACA,MAAME,MAAM,GAAGhC,iBAAiB,CAACoB,QAAQ,CAAC;MAC1C,MAAMa,SAAS,GAAG;QAChBA,SAAS,EAAEF,kBAAkB;QAC7BG,eAAe,EAAEF,MAAM,CAAC9B,IAAI;QAC5BiC,SAAS,EAAEH,MAAM,CAAC7B;MACpB,CAAC;MAED,OAAO;QAAE7D,OAAO,EAAE,IAAI;QAAE2F,SAAS;QAAE9B,KAAK,EAAE6B,MAAM,CAAC7B;MAAM,CAAC;IAC1D,CAAC,CAAC,OAAO1D,KAAK,EAAE;MAAA,IAAA2F,sBAAA;MACd1F,OAAO,CAACD,KAAK,CAAC,+BAA+B2E,QAAQ,GAAG,EAAE3E,KAAK,CAAC;MAChE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG,KAAK,EAAEA,KAAK,CAACE,OAAO;QAAEwD,KAAK,EAAE,EAAAiC,sBAAA,GAAApC,iBAAiB,CAACoB,QAAQ,CAAC,cAAAgB,sBAAA,uBAA3BA,sBAAA,CAA6BjC,KAAK,KAAIiB;MAAS,CAAC;IACxG;EACF,CAAC;;EAED;EACA,MAAMiB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACzC,iBAAiB,CAAC;;MAEhD;MACAuC,SAAS,CAACG,OAAO,CAACtB,QAAQ,IAAI;QAC5B,MAAMuB,WAAW,GAAG,CAAC,CAACrB,MAAM,CAACF,QAAQ,CAAC;QACtC,IAAI,CAACuB,WAAW,EAAE;UAChBjG,OAAO,CAACkG,IAAI,CAAC,8BAA8BxB,QAAQ,MAAMpB,iBAAiB,CAACoB,QAAQ,CAAC,CAACjB,KAAK,EAAE,CAAC;QAC/F;MACF,CAAC,CAAC;;MAEF;MACA,KAAK,MAAMiB,QAAQ,IAAImB,SAAS,EAAE;QAChC,IAAIjB,MAAM,CAACF,QAAQ,CAAC,EAAE;UACpB,MAAMyB,MAAM,GAAG,MAAM1B,qBAAqB,CAACC,QAAQ,CAAC;UACpDkB,YAAY,CAACQ,IAAI,CAACD,MAAM,CAAC;;UAEzB;UACA,MAAM,IAAIpE,OAAO,CAACC,OAAO,IAAI8C,UAAU,CAAC9C,OAAO,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC,MAAM;UACLhC,OAAO,CAACkG,IAAI,CAAC,wBAAwBxB,QAAQ,oCAAoC,CAAC;QACpF;MACF;MAEA,MAAM2B,UAAU,GAAGT,YAAY,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3G,OAAO,CAAC;MACtD,MAAM4G,MAAM,GAAGZ,YAAY,CAACU,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC3G,OAAO,CAAC;MAEnD,IAAIyG,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;QACzB;QACA,MAAMC,cAAc,GAAGL,UAAU,CAACM,GAAG,CAACR,MAAM,IAAIA,MAAM,CAACZ,SAAS,CAAC;;QAEjE;QACA,MAAMqB,cAAc,GAAGrL,QAAQ,IAAIsL,IAAI,CAACC,GAAG,CAAC,CAAC;QAC7C,MAAMC,aAAa,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;QAE9C,MAAMC,cAAc,GAAG,MAAM1M,gBAAgB,CAACmM,cAAc,EAAErL,SAAS,EAAEuL,cAAc,EAAEG,aAAa,CAAC;QAEvG,IAAIE,cAAc,CAACtH,IAAI,CAACC,OAAO,EAAE;UAC/B,MAAMsH,aAAa,GAAGD,cAAc,CAACtH,IAAI,CAACA,IAAI;UAC9C;UACA;UACA;UACA;UACA;QAEF,CAAC,MAAM;UACLK,OAAO,CAACD,KAAK,CAAC,2CAA2C,CAAC;QAC5D;MACF;IACF,CAAC,CAAC,OAAOoH,UAAU,EAAE;MACnBnH,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEoH,UAAU,CAAC;MAC5D;IACF;EACF,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF7J,kBAAkB,CAAC,IAAI,CAAC;MACxBoI,wBAAwB,CAAC,CAAC;MAC1BxI,iBAAiB,CAAC,mBAAmB,CAAC;MACtCF,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMoK,YAAY,GAAG/E,QAAQ,CAACgF,aAAa,CAAC,qCAAqC,CAAC;MAClF,IAAI,CAACD,YAAY,EAAE;QACjB,MAAM,IAAIvH,KAAK,CAAC,+CAA+C,CAAC;MAClE;;MAEA;MACA,MAAM,IAAIiC,OAAO,CAACC,OAAO,IAAI8C,UAAU,CAAC9C,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAMuF,sBAAsB,GAAIC,SAAS,IAAK;QAC5C;QACA,MAAMC,eAAe,GAAGD,SAAS,CAACE,gBAAgB,CAAC,iBAAiB,CAAC;QAErED,eAAe,CAACzB,OAAO,CAAC,CAAC2B,cAAc,EAAEC,KAAK,KAAK;UACjD,IAAI;YACF;YACA,MAAMC,WAAW,GAAGF,cAAc,CAACD,gBAAgB,CAAC,YAAY,CAAC;YACjEG,WAAW,CAAC7B,OAAO,CAAC8B,KAAK,IAAI;cAC3B;cACA,MAAMC,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACF,KAAK,CAAC;;cAEpD;cACA,IAAIA,KAAK,CAACG,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;gBACzCJ,KAAK,CAACK,KAAK,CAACzF,KAAK,GAAGqF,aAAa,CAACrF,KAAK;gBACvCoF,KAAK,CAACK,KAAK,CAACxF,MAAM,GAAGoF,aAAa,CAACpF,MAAM;gBACzCmF,KAAK,CAACK,KAAK,CAACC,QAAQ,GAAG,SAAS;cAClC;;cAEA;cACA,IAAIL,aAAa,CAACM,MAAM,IAAIN,aAAa,CAACM,MAAM,KAAK,MAAM,EAAE;gBAC3DP,KAAK,CAACK,KAAK,CAACE,MAAM,GAAGN,aAAa,CAACM,MAAM;cAC3C;cACA,IAAIN,aAAa,CAACO,WAAW,IAAIP,aAAa,CAACO,WAAW,KAAK,KAAK,EAAE;gBACpER,KAAK,CAACK,KAAK,CAACG,WAAW,GAAGP,aAAa,CAACO,WAAW;cACrD;cACA,IAAIP,aAAa,CAACQ,IAAI,IAAIR,aAAa,CAACQ,IAAI,KAAK,MAAM,EAAE;gBACvDT,KAAK,CAACK,KAAK,CAACI,IAAI,GAAGR,aAAa,CAACQ,IAAI;cACvC;cACA,IAAIR,aAAa,CAACS,eAAe,IAAIT,aAAa,CAACS,eAAe,KAAK,MAAM,EAAE;gBAC7EV,KAAK,CAACK,KAAK,CAACK,eAAe,GAAGT,aAAa,CAACS,eAAe;cAC7D;;cAEA;cACA,IAAIT,aAAa,CAACU,SAAS,IAAIV,aAAa,CAACU,SAAS,KAAK,MAAM,EAAE;gBACjEX,KAAK,CAACK,KAAK,CAACM,SAAS,GAAGV,aAAa,CAACU,SAAS;cACjD;YACF,CAAC,CAAC;;YAEF;YACA,MAAMC,aAAa,GAAGf,cAAc,CAACgB,qBAAqB,CAAC,CAAC;YAC5DhB,cAAc,CAACQ,KAAK,CAACzF,KAAK,GAAGgG,aAAa,CAAChG,KAAK,GAAG,IAAI;YACvDiF,cAAc,CAACQ,KAAK,CAACxF,MAAM,GAAG+F,aAAa,CAAC/F,MAAM,GAAG,IAAI;YACzDgF,cAAc,CAACQ,KAAK,CAACS,QAAQ,GAAG,UAAU;YAC1CjB,cAAc,CAACQ,KAAK,CAACC,QAAQ,GAAG,SAAS;;YAEzC;YACA,MAAMS,YAAY,GAAGlB,cAAc,CAACD,gBAAgB,CAAC,uBAAuB,CAAC;YAC7EmB,YAAY,CAAC7C,OAAO,CAAC8C,OAAO,IAAI;cAC9B,MAAMf,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACc,OAAO,CAAC;;cAEtD;cACA,IAAIf,aAAa,CAACa,QAAQ,IAAIb,aAAa,CAACa,QAAQ,KAAK,QAAQ,EAAE;gBACjEE,OAAO,CAACX,KAAK,CAACS,QAAQ,GAAGb,aAAa,CAACa,QAAQ;gBAC/CE,OAAO,CAACX,KAAK,CAACY,GAAG,GAAGhB,aAAa,CAACgB,GAAG;gBACrCD,OAAO,CAACX,KAAK,CAACa,IAAI,GAAGjB,aAAa,CAACiB,IAAI;gBACvCF,OAAO,CAACX,KAAK,CAACc,KAAK,GAAGlB,aAAa,CAACkB,KAAK;gBACzCH,OAAO,CAACX,KAAK,CAACe,MAAM,GAAGnB,aAAa,CAACmB,MAAM;cAC7C;;cAEA;cACAJ,OAAO,CAACX,KAAK,CAACzF,KAAK,GAAGqF,aAAa,CAACrF,KAAK;cACzCoG,OAAO,CAACX,KAAK,CAACxF,MAAM,GAAGoF,aAAa,CAACpF,MAAM;;cAE3C;cACAmG,OAAO,CAACX,KAAK,CAACgB,OAAO,GAAGpB,aAAa,CAACoB,OAAO;cAC7CL,OAAO,CAACX,KAAK,CAACiB,UAAU,GAAGrB,aAAa,CAACqB,UAAU;cACnDN,OAAO,CAACX,KAAK,CAACkB,OAAO,GAAGtB,aAAa,CAACsB,OAAO;YAC/C,CAAC,CAAC;;YAEF;YACA,MAAMC,SAAS,GAAG3B,cAAc,CAACD,gBAAgB,CAAC,4EAA4E,CAAC;YAC/H4B,SAAS,CAACtD,OAAO,CAACuD,IAAI,IAAI;cACxB,MAAMxB,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACuB,IAAI,CAAC;cACnDA,IAAI,CAACpB,KAAK,CAACE,MAAM,GAAGN,aAAa,CAACM,MAAM;cACxCkB,IAAI,CAACpB,KAAK,CAACG,WAAW,GAAGP,aAAa,CAACO,WAAW;cAClDiB,IAAI,CAACpB,KAAK,CAACK,eAAe,GAAGT,aAAa,CAACS,eAAe;cAC1De,IAAI,CAACpB,KAAK,CAACkB,OAAO,GAAGtB,aAAa,CAACsB,OAAO;YAC5C,CAAC,CAAC;;YAEF;YACA,MAAMG,SAAS,GAAG7B,cAAc,CAACD,gBAAgB,CAAC,gDAAgD,CAAC;YACnG8B,SAAS,CAACxD,OAAO,CAACuD,IAAI,IAAI;cACxB,MAAMxB,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACuB,IAAI,CAAC;cACnDA,IAAI,CAACpB,KAAK,CAACE,MAAM,GAAGN,aAAa,CAACM,MAAM;cACxCkB,IAAI,CAACpB,KAAK,CAACG,WAAW,GAAGP,aAAa,CAACO,WAAW;cAClDiB,IAAI,CAACpB,KAAK,CAACkB,OAAO,GAAGtB,aAAa,CAACsB,OAAO;YAC5C,CAAC,CAAC;;YAEF;YACA,MAAMI,cAAc,GAAG9B,cAAc,CAACD,gBAAgB,CAAC,6EAA6E,CAAC;YACrI+B,cAAc,CAACzD,OAAO,CAAC8C,OAAO,IAAI;cAChC,MAAMf,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACc,OAAO,CAAC;cACtD,IAAIf,aAAa,CAACQ,IAAI,EAAEO,OAAO,CAACX,KAAK,CAACI,IAAI,GAAGR,aAAa,CAACQ,IAAI;cAC/D,IAAIR,aAAa,CAACM,MAAM,EAAES,OAAO,CAACX,KAAK,CAACE,MAAM,GAAGN,aAAa,CAACM,MAAM;cACrE,IAAIN,aAAa,CAACO,WAAW,EAAEQ,OAAO,CAACX,KAAK,CAACG,WAAW,GAAGP,aAAa,CAACO,WAAW;cACpF,IAAIP,aAAa,CAACsB,OAAO,EAAEP,OAAO,CAACX,KAAK,CAACkB,OAAO,GAAGtB,aAAa,CAACsB,OAAO;YAC1E,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOtJ,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB6H,KAAK,GAAG,EAAE7H,KAAK,CAAC;UACxD;QACF,CAAC,CAAC;;QAEF;QACA,MAAM2J,OAAO,GAAGlC,SAAS,CAACE,gBAAgB,CAAC,oBAAoB,CAAC;QAEhEgC,OAAO,CAAC1D,OAAO,CAAC,CAAC2D,MAAM,EAAE/B,KAAK,KAAK;UACjC,IAAI;YACF;YACA,MAAMgC,IAAI,GAAGD,MAAM,CAAChB,qBAAqB,CAAC,CAAC;;YAE3C;YACA,IAAIhB,cAAc,GAAGgC,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;YACtD,IAAI,CAAClC,cAAc,EAAE;cACnB;cACAA,cAAc,GAAGgC,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;YAClD;YACA,IAAI,CAAClC,cAAc,EAAE;cACnB;cACAA,cAAc,GAAGgC,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;YAClD;YAEA,IAAIlC,cAAc,EAAE;cAClB,MAAMmC,UAAU,GAAGnC,cAAc,CAACgB,qBAAqB,CAAC,CAAC;;cAEzD;cACA,MAAMoB,WAAW,GAAGH,IAAI,CAACb,GAAG,GAAGe,UAAU,CAACf,GAAG;cAC7C,MAAMiB,YAAY,GAAGJ,IAAI,CAACZ,IAAI,GAAGc,UAAU,CAACd,IAAI;;cAEhD;cACAW,MAAM,CAACM,YAAY,CAAC,wBAAwB,EAAEN,MAAM,CAACxB,KAAK,CAACS,QAAQ,IAAI,EAAE,CAAC;cAC1Ee,MAAM,CAACM,YAAY,CAAC,mBAAmB,EAAEN,MAAM,CAACxB,KAAK,CAACY,GAAG,IAAI,EAAE,CAAC;cAChEY,MAAM,CAACM,YAAY,CAAC,oBAAoB,EAAEN,MAAM,CAACxB,KAAK,CAACa,IAAI,IAAI,EAAE,CAAC;;cAElE;cACAW,MAAM,CAACxB,KAAK,CAACS,QAAQ,GAAG,UAAU;cAClCe,MAAM,CAACxB,KAAK,CAACY,GAAG,GAAGmB,IAAI,CAAC1K,GAAG,CAAC,CAAC,EAAEuK,WAAW,CAAC,GAAG,IAAI;cAClDJ,MAAM,CAACxB,KAAK,CAACa,IAAI,GAAGgB,YAAY,GAAG,IAAI;cACvCL,MAAM,CAACxB,KAAK,CAACc,KAAK,GAAG,MAAM;cAC3BU,MAAM,CAACxB,KAAK,CAACe,MAAM,GAAG,MAAM;cAC5BS,MAAM,CAACxB,KAAK,CAACM,SAAS,GAAG,MAAM;cAC/BkB,MAAM,CAACxB,KAAK,CAACgC,MAAM,GAAG,IAAI;;cAE1B;cACA,MAAMC,iBAAiB,GAAGxF,MAAM,CAACoD,gBAAgB,CAACL,cAAc,CAAC,CAACiB,QAAQ;cAC1E,IAAIwB,iBAAiB,KAAK,QAAQ,EAAE;gBAClCzC,cAAc,CAACQ,KAAK,CAACS,QAAQ,GAAG,UAAU;cAC5C;;cAEA;cACA,MAAMyB,cAAc,GAAGV,MAAM,CAACjC,gBAAgB,CAAC,GAAG,CAAC;cACnD2C,cAAc,CAACrE,OAAO,CAACsE,EAAE,IAAI;gBAC3B,MAAMvC,aAAa,GAAGnD,MAAM,CAACoD,gBAAgB,CAACsC,EAAE,CAAC;gBACjD,IAAIvC,aAAa,CAACjJ,KAAK,EAAEwL,EAAE,CAACnC,KAAK,CAACrJ,KAAK,GAAGiJ,aAAa,CAACjJ,KAAK;gBAC7D,IAAIiJ,aAAa,CAAClJ,QAAQ,EAAEyL,EAAE,CAACnC,KAAK,CAACtJ,QAAQ,GAAGkJ,aAAa,CAAClJ,QAAQ;gBACtE,IAAIkJ,aAAa,CAACwC,UAAU,EAAED,EAAE,CAACnC,KAAK,CAACoC,UAAU,GAAGxC,aAAa,CAACwC,UAAU;gBAC5E,IAAIxC,aAAa,CAACyC,UAAU,EAAEF,EAAE,CAACnC,KAAK,CAACqC,UAAU,GAAGzC,aAAa,CAACyC,UAAU;cAC9E,CAAC,CAAC;YACJ,CAAC,MAAM;cACLxK,OAAO,CAACkG,IAAI,CAAC,6CAA6C0B,KAAK,EAAE,CAAC;;cAElE;cACA+B,MAAM,CAACxB,KAAK,CAACS,QAAQ,GAAG,UAAU;cAClCe,MAAM,CAACxB,KAAK,CAACY,GAAG,GAAG,MAAM;cACzBY,MAAM,CAACxB,KAAK,CAACa,IAAI,GAAG,MAAM;cAC1BW,MAAM,CAACxB,KAAK,CAACc,KAAK,GAAG,MAAM;cAC3BU,MAAM,CAACxB,KAAK,CAACM,SAAS,GAAG,MAAM;cAC/BkB,MAAM,CAACxB,KAAK,CAACsC,MAAM,GAAG,WAAW;cACjCd,MAAM,CAACxB,KAAK,CAACgB,OAAO,GAAG,MAAM;cAC7BQ,MAAM,CAACxB,KAAK,CAACuC,cAAc,GAAG,QAAQ;YACxC;UAEF,CAAC,CAAC,OAAO3K,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB6H,KAAK,GAAG,EAAE7H,KAAK,CAAC;;YAEvD;YACA4J,MAAM,CAACxB,KAAK,CAACS,QAAQ,GAAG,QAAQ;YAChCe,MAAM,CAACxB,KAAK,CAACsC,MAAM,GAAG,WAAW;YACjCd,MAAM,CAACxB,KAAK,CAACgB,OAAO,GAAG,MAAM;YAC7BQ,MAAM,CAACxB,KAAK,CAACuC,cAAc,GAAG,QAAQ;UACxC;QACF,CAAC,CAAC;MACJ,CAAC;;MAED;MACAnD,sBAAsB,CAACF,YAAY,CAAC;;MAEpC;MACA,MAAM,IAAItF,OAAO,CAACC,OAAO,IAAI8C,UAAU,CAAC9C,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAM2I,aAAa,GAAGtD,YAAY,CAACuD,SAAS,CAAC,IAAI,CAAC;;MAElD;MACA,MAAMC,YAAY,GAAGF,aAAa,CAACjD,gBAAgB,CAAC,iBAAiB,CAAC;MACtEmD,YAAY,CAAC7E,OAAO,CAAC,CAACrB,KAAK,EAAEiD,KAAK,KAAK;QACrC;QACA,MAAM0B,SAAS,GAAG3E,KAAK,CAAC+C,gBAAgB,CAAC,4EAA4E,CAAC;QACtH,MAAMoD,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;QAE3BzB,SAAS,CAACtD,OAAO,CAACuD,IAAI,IAAI;UACxB,MAAMyB,OAAO,GAAG,GAAGzB,IAAI,CAAC0B,YAAY,CAAC,IAAI,CAAC,IAAI1B,IAAI,CAAC0B,YAAY,CAAC,IAAI,CAAC,IAAI1B,IAAI,CAAC0B,YAAY,CAAC,IAAI,CAAC,IAAI1B,IAAI,CAAC0B,YAAY,CAAC,IAAI,CAAC,EAAE;UAC7H,IAAIH,SAAS,CAACI,GAAG,CAACF,OAAO,CAAC,EAAE;YAC1B;YACAzB,IAAI,CAAC4B,MAAM,CAAC,CAAC;UACf,CAAC,MAAM;YACLL,SAAS,CAACM,GAAG,CAACJ,OAAO,CAAC;UACxB;QACF,CAAC,CAAC;;QAEF;QACArG,KAAK,CAACwD,KAAK,CAACkD,SAAS,GAAG,MAAM;QAC9B1G,KAAK,CAACwD,KAAK,CAACmD,SAAS,GAAG,MAAM;MAChC,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAGZ,aAAa,CAACjD,gBAAgB,CAAC,oBAAoB,CAAC;MAC1E6D,aAAa,CAACvF,OAAO,CAAC,CAAC2D,MAAM,EAAE/B,KAAK,KAAK;QACvC;QACA,MAAM4D,QAAQ,GAAGC,UAAU,CAAC9B,MAAM,CAACxB,KAAK,CAACY,GAAG,CAAC,IAAI,CAAC;QAClD,IAAIyC,QAAQ,GAAG,GAAG,EAAE;UAClB7B,MAAM,CAACxB,KAAK,CAACY,GAAG,GAAG,MAAM;UACzBY,MAAM,CAACxB,KAAK,CAACS,QAAQ,GAAG,UAAU;UAClCe,MAAM,CAACxB,KAAK,CAACsC,MAAM,GAAG,qBAAqB;UAC3Cd,MAAM,CAACxB,KAAK,CAACgB,OAAO,GAAG,MAAM;UAC7BQ,MAAM,CAACxB,KAAK,CAACuC,cAAc,GAAG,QAAQ;QACxC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMgB,UAAU,GAAGf,aAAa,CAACjD,gBAAgB,CAAC,eAAe,CAAC;MAClEgE,UAAU,CAAC1F,OAAO,CAAE2F,SAAS,IAAK;QAChC,MAAMC,eAAe,GAAGD,SAAS,CAACrE,aAAa,CAAC,mBAAmB,CAAC;QACpE,MAAMuE,YAAY,GAAGF,SAAS,CAACrE,aAAa,CAAC,gBAAgB,CAAC;QAC9D,MAAMwE,UAAU,GAAGH,SAAS,CAACrE,aAAa,CAAC,eAAe,CAAC;QAE3D,IAAI5I,MAAM,GAAGkN,eAAe,IAAIC,YAAY;;QAE5C;QACA;QACA,MAAME,UAAU,GAAGJ,SAAS,CAACrE,aAAa,CAAC,YAAY,CAAC,KAAK,IAAI;QACjE,MAAM0E,eAAe,GAAGL,SAAS,CAACM,WAAW,CAACC,QAAQ,CAAC,mBAAmB,CAAC;QAC3E,MAAMC,kBAAkB,GAAGR,SAAS,CAACrE,aAAa,CAAC,oBAAoB,CAAC,KAAK,IAAI;QAEjF,MAAM8E,eAAe,GAAGL,UAAU,IAAKC,eAAe,IAAIG,kBAAmB;;QAE7E;QACA;QACA,MAAME,0BAA0B,GAAGD,eAAe,IAC/CT,SAAS,CAACrE,aAAa,CAAC,YAAY,CAAC,IAAIqE,SAAS,CAACW,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAE,IAC9FZ,SAAS,CAACM,WAAW,CAACC,QAAQ,CAAC,eAAe,CAAC;QAEjD,IAAIxN,MAAM,EAAE;UACV,IAAI,CAACA,MAAM,CAAC4N,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAClD7N,MAAM,CAAC4N,SAAS,CAAClB,GAAG,CAAC,kBAAkB,CAAC;UAC1C;;UAEA;UACA,IAAIgB,eAAe,EAAE;YACnBT,SAAS,CAACW,SAAS,CAAClB,GAAG,CAAC,0BAA0B,CAAC;YACnD1M,MAAM,CAAC4N,SAAS,CAAClB,GAAG,CAAC,uBAAuB,CAAC;;YAE7C;YACAO,SAAS,CAAC1B,YAAY,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;;YAE1D;YACA0B,SAAS,CAACxD,KAAK,CAACqE,IAAI,GAAG,gBAAgB;YACvCb,SAAS,CAACxD,KAAK,CAACsE,eAAe,GAAG,QAAQ;;YAE1C;YACAd,SAAS,CAACxD,KAAK,CAACuE,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE,WAAW,CAAC;UACpE;;UAEA;UACA,IAAIL,0BAA0B,EAAE;YAC9B3N,MAAM,CAAC4N,SAAS,CAAClB,GAAG,CAAC,kBAAkB,CAAC;;YAExC;YACA1M,MAAM,CAACyJ,KAAK,CAACgB,OAAO,GAAG,oBAAoB;YAC3CzK,MAAM,CAACyJ,KAAK,CAACwE,eAAe,GAAG,OAAO;YACtCjO,MAAM,CAACyJ,KAAK,CAACyE,cAAc,GAAG,OAAO;UACvC;UAEA,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACpB,SAAS,CAACqB,QAAQ,CAAC;UAClD,IAAIC,iBAAiB;UAErB,IAAIrB,eAAe,EAAE;YACnBqB,iBAAiB,GAAGJ,WAAW,CAACvG,MAAM,CAAC4G,KAAK,IAC1C,CAACA,KAAK,CAACZ,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAC9C,CAAC;UACH,CAAC,MAAM,IAAIV,YAAY,EAAE;YACvBoB,iBAAiB,GAAGJ,WAAW,CAACvG,MAAM,CAAC4G,KAAK,IAC1C,CAACA,KAAK,CAACZ,SAAS,CAACC,QAAQ,CAAC,eAAe,CAC3C,CAAC;UACH;UAEA,IAAIU,iBAAiB,IAAIA,iBAAiB,CAACxG,MAAM,GAAG,CAAC,EAAE;YACrD,MAAM0G,YAAY,GAAG7K,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAClD4K,YAAY,CAACC,SAAS,GAAG,mBAAmB;YAE5CH,iBAAiB,CAACjH,OAAO,CAACkH,KAAK,IAAI;cACjCC,YAAY,CAACE,WAAW,CAACH,KAAK,CAAC;YACjC,CAAC,CAAC;YAEFvB,SAAS,CAAC0B,WAAW,CAACF,YAAY,CAAC;UACrC;UAEAxB,SAAS,CAACxD,KAAK,CAACgB,OAAO,GAAG,OAAO;UACjCwC,SAAS,CAACxD,KAAK,CAACzF,KAAK,GAAG,MAAM;QAChC;;QAEA;QACA,IAAI2J,0BAA0B,EAAE;UAC9B,MAAMiB,MAAM,GAAG3B,SAAS,CAACjE,gBAAgB,CAAC,OAAO,CAAC;UAClD4F,MAAM,CAACtH,OAAO,CAACuH,KAAK,IAAI;YACtB,MAAMC,KAAK,GAAGD,KAAK,CAACjG,aAAa,CAAC,OAAO,CAAC;YAC1C,IAAIkG,KAAK,EAAE;cACTA,KAAK,CAACrF,KAAK,CAACgB,OAAO,GAAG,oBAAoB;cAC1CqE,KAAK,CAACrF,KAAK,CAACwE,eAAe,GAAG,OAAO;;cAErC;cACAa,KAAK,CAAClB,SAAS,CAAClB,GAAG,CAAC,wBAAwB,CAAC;;cAE7C;cACAmC,KAAK,CAACpF,KAAK,CAACsF,cAAc,GAAG,UAAU;YACzC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAW,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY/C,aAAa,CAACgD,SAAS;AACnC;AACA;AACA;AACA,KAAK;;MAEC;;MAEA;MACA,MAAM;QAAEC;MAAoB,CAAC,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;MAClE,MAAMlO,QAAQ,GAAG,MAAMkO,mBAAmB,CAACF,WAAW,EAAE;QACtDG,MAAM,EAAE,IAAI;QACZC,WAAW,EAAE,UAAU;QACvBC,eAAe,EAAE,IAAI;QACrBtD,MAAM,EAAE;UACN1B,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfF,IAAI,EAAE;QACR,CAAC;QACDgF,iBAAiB,EAAE,IAAI,CAAE;MAC3B,CAAC,CAAC;MAEF,IAAItO,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACsO,GAAG,EAAE;QACzC,MAAMC,QAAQ,GAAG,iBAAiB,IAAIrH,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACmH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;QAC9E,MAAMC,eAAe,GAAGnU,qBAAqB,CAACyF,QAAQ,CAACC,IAAI,CAACsO,GAAG,EAAEC,QAAQ,CAAC;QAE1E,IAAIE,eAAe,EAAE;UACnBjR,iBAAiB,CAAC,8BAA8B,CAAC;UACjDF,cAAc,CAAC,IAAI,CAAC;QACtB,CAAC,MAAM;UACL,MAAM,IAAI6C,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAACJ,QAAQ,CAACO,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C5C,iBAAiB,CAAC,2CAA2C,CAAC;MAC9DF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA1F,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC+M,MAAM,CAAClK,UAAU,EAAE;MACtBkK,MAAM,CAAClK,UAAU,GAAGA,UAAU;IAChC;IAEA6G,kBAAkB,CAAC,CAAC;IACpB8M,iCAAiC,CAAC,CAAC;IACnC5O,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACpE,SAAS,EAAEE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE3B;EACA1D,SAAS,CAAC,MAAM;IACd;IACA,IAAI+D,SAAS,IAAIoC,mBAAmB,IAAIA,mBAAmB,CAAC0C,gBAAgB,KAAK,WAAW,IAAI,CAACxC,oBAAoB,EAAE;MACrHoQ,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM;MACL;MACAC,kBAAkB,CAAC,CAAC;IACtB;;IAEA;IACA,OAAO,MAAM;MACXA,kBAAkB,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,CAAC3S,SAAS,EAAEoC,mBAAmB,EAAEE,oBAAoB,CAAC,CAAC;;EAE1D;EACA,MAAMmQ,iCAAiC,GAAG,MAAAA,CAAA,KAAY;IACpD,IAAI;MACF;MACA,MAAM3N,gBAAgB,GAAG,MAAMD,6BAA6B,CAAC,CAAC;;MAE9D;MACA,IAAIC,gBAAgB,IAAIA,gBAAgB,CAACA,gBAAgB,KAAK,WAAW,EAAE;QACzE,MAAMiB,eAAe,CAAC,CAAC;MACzB,CAAC,MAAM;QACL;QACAhG,gBAAgB,CAAC,KAAK,CAAC;QACvBE,YAAY,CAAC,qCAAqC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOkE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDpE,gBAAgB,CAAC,KAAK,CAAC;MACvBE,YAAY,CAAC,6BAA6B,CAAC;IAC7C;EACF,CAAC;EAED,MAAM2S,qBAAqB,GAAGA,CAAA,KAAM;IAClChQ,QAAQ,CAAC,YAAYnD,SAAS,EAAE,EAAE;MAChCoT,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAU;IAChC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,KAAK;IACxD;IACA,IAAID,QAAQ,KAAK,UAAU,EAAE;MAC3B,MAAME,WAAW,GAAGzP,mBAAmB,CAACsP,OAAO,CAAC;MAChD,IAAIG,WAAW,EAAE;QACfD,KAAK,GAAG5E,IAAI,CAAC1K,GAAG,CAACuP,WAAW,CAACxP,GAAG,EAAE2K,IAAI,CAAC3K,GAAG,CAACwP,WAAW,CAACvP,GAAG,EAAEsP,KAAK,CAAC,CAAC;MACrE;IACF;IAEA/S,mBAAmB,CAACiT,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACJ,OAAO,GAAG;QACT,GAAGI,IAAI,CAACJ,OAAO,CAAC;QAChB,CAACC,QAAQ,GAAGC;MACd;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhS,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiS,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFzR,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3B,MAAMiC,QAAQ,GAAG,MAAMrF,sBAAsB,CAACyB,gBAAgB,CAAC;MAE/D,IAAI4D,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QAC1CmB,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACpF,gBAAgB,CAAC,CAAC;QAC1EG,kBAAkB,CAACH,gBAAgB,CAAC;QACpCqB,iBAAiB,CAAC,0CAA0C,CAAC;QAC7DF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACL,MAAM,IAAI6C,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD5C,iBAAiB,CAAC,4CAA4C,CAAC;MAC/DF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRQ,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM0R,UAAU,GAAGA,CAAA,KAAM;IACvBtR,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMuR,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCvR,uBAAuB,CAAC,KAAK,CAAC;IAC9B,MAAMqR,iBAAiB,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxR,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMyR,oBAAoB,GAAGA,CAAA,KAAM;IACjCvR,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMwR,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACAhB,kBAAkB,CAAC,CAAC;IACpB/P,QAAQ,CAAC,YAAYnD,SAAS,EAAE,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiT,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,kBAAkB,CAAC,CAAC;;IAEpB;IACAlQ,oBAAoB,CAAC,EAAE,CAAC;;IAExB;IACA,MAAMmR,OAAO,GAAGC,WAAW,CAAC,MAAM;MAChCpR,oBAAoB,CAAC2Q,IAAI,IAAI;QAC3B,IAAIA,IAAI,IAAI,CAAC,EAAE;UACb;UACAU,aAAa,CAACF,OAAO,CAAC;UACtBjR,mBAAmB,CAAC,IAAI,CAAC;UACzBC,QAAQ,CAAC,YAAYnD,SAAS,EAAE,CAAC;UACjC,OAAO,CAAC;QACV;QACA,OAAO2T,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;;IAER;IACAzQ,mBAAmB,CAACiR,OAAO,CAAC;EAC9B,CAAC;EAED,MAAMjB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIjQ,gBAAgB,EAAE;MACpBoR,aAAa,CAACpR,gBAAgB,CAAC;MAC/BC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;IACAF,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMsR,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFhS,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE9B,IAAIiS,aAAa,GAAGnR,eAAe,CAAC,CAAC;MACrC,IAAIoR,YAAY,GAAG,0DAA0D;;MAE7E;MACA,IAAIvT,WAAW,EAAE;QACf,IAAIA,WAAW,CAACwT,OAAO,EAAE;UACvB;UACAF,aAAa,GAAGnR,eAAe;UAC/BoR,YAAY,GAAG,iEAAiE;QAClF,CAAC,MAAM;UACL;UACA,IAAI;YACF,MAAME,cAAc,GAAG,MAAMvV,iBAAiB,CAAC,WAAW,CAAC;YAC3D,IAAIuV,cAAc,CAACpQ,IAAI,IAAIoQ,cAAc,CAACpQ,IAAI,CAACC,OAAO,EAAE;cACtDgQ,aAAa,GAAGG,cAAc,CAACpQ,IAAI,CAACA,IAAI,CAACiB,QAAQ;cACjDiP,YAAY,GAAG,iEAAiE;YAClF,CAAC,MAAM;cACL7P,OAAO,CAACkG,IAAI,CAAC,2DAA2D,CAAC;YAC3E;UACF,CAAC,CAAC,OAAO8J,WAAW,EAAE;YACpBhQ,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEiQ,WAAW,CAAC;YAC7DhQ,OAAO,CAACkG,IAAI,CAAC,sCAAsC,CAAC;UACtD;QACF;MACF;;MAEA;MACAnK,mBAAmB,CAAC6T,aAAa,CAAC;;MAElC;MACA,MAAMlQ,QAAQ,GAAG,MAAMrF,sBAAsB,CAACuV,aAAa,CAAC;MAE5D,IAAIlQ,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QAC1CmB,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAC0O,aAAa,CAAC,CAAC;QACvE3T,kBAAkB,CAAC2T,aAAa,CAAC;QACjCzS,iBAAiB,CAAC0S,YAAY,CAAC;QAC/B5S,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACL,MAAM,IAAI6C,KAAK,CAAC,wCAAwC,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D;MACAhE,mBAAmB,CAAC0C,eAAe,CAAC;MACpCxC,kBAAkB,CAACwC,eAAe,CAAC;MACnCsC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACzC,eAAe,CAAC,CAAC;MACzEtB,iBAAiB,CAAC,qFAAqF,CAAC;MACxGF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRU,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC/BI,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMkS,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlS,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;EAGD,MAAMmS,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMxP,gBAAgB,GAAG,MAAMD,6BAA6B,CAAC,CAAC;MAE9D,IAAIC,gBAAgB,IAAIA,gBAAgB,CAACA,gBAAgB,KAAK,WAAW,EAAE;QACzE,MAAMiB,eAAe,CAAC,CAAC;QACvBlC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBtC,iBAAiB,CAAC,6BAA6B,CAAC;MAClD,CAAC,MAAM;QACLA,iBAAiB,CAAC,qDAAqD,CAAC;MAC1E;MACAF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C5C,iBAAiB,CAAC,0CAA0C,CAAC;MAC7DF,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;;EAID;EACA,MAAMkT,aAAa,GAAIvR,QAAQ,IAAK;IAClC,MAAMwR,OAAO,GAAG;MACd,SAAS,EAAE,KAAK;MAChB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,OAAO,CAACxR,QAAQ,CAAC,IAAI,KAAK;EACnC,CAAC;EAED,MAAMyR,cAAc,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC5BlG,UAAU,EAAE,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAAwU,qBAAA,GAAhBxU,gBAAgB,CAAE4C,MAAM,cAAA4R,qBAAA,uBAAxBA,qBAAA,CAA0B3R,SAAS,KAAI,WAAW;MAC9D6L,UAAU,EAAE2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAAyU,sBAAA,GAAhBzU,gBAAgB,CAAE4C,MAAM,cAAA6R,sBAAA,uBAAxBA,sBAAA,CAA0B3R,QAAQ,KAAI,MAAM,CAAC;MACvEC,QAAQ,EAAE,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAA0U,sBAAA,GAAhB1U,gBAAgB,CAAE4C,MAAM,cAAA8R,sBAAA,uBAAxBA,sBAAA,CAA0B3R,QAAQ,KAAI,EAAE,IAAI;MACzDC,KAAK,EAAE,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAA2U,sBAAA,GAAhB3U,gBAAgB,CAAE4C,MAAM,cAAA+R,sBAAA,uBAAxBA,sBAAA,CAA0B3R,KAAK,KAAI,SAAS;MACnD4R,YAAY,EAAE,aAAa;MAC3BjG,MAAM,EAAE;IACV,CAAC;EAAA,CAAC;EAEF,MAAMkG,eAAe,GAAGA,CAAA;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC7BxG,UAAU,EAAE,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAA8U,sBAAA,GAAhB9U,gBAAgB,CAAEiD,OAAO,cAAA6R,sBAAA,uBAAzBA,sBAAA,CAA2BjS,SAAS,KAAI,WAAW;MAC/D6L,UAAU,EAAE2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAA+U,sBAAA,GAAhB/U,gBAAgB,CAAEiD,OAAO,cAAA8R,sBAAA,uBAAzBA,sBAAA,CAA2BjS,QAAQ,KAAI,MAAM,CAAC;MACxEC,QAAQ,EAAE,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAAgV,sBAAA,GAAhBhV,gBAAgB,CAAEiD,OAAO,cAAA+R,sBAAA,uBAAzBA,sBAAA,CAA2BjS,QAAQ,KAAI,EAAE,IAAI;MAC1DC,KAAK,EAAE,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAAiV,sBAAA,GAAhBjV,gBAAgB,CAAEiD,OAAO,cAAAgS,sBAAA,uBAAzBA,sBAAA,CAA2BjS,KAAK,KAAI;IAC7C,CAAC;EAAA,CAAC;EAEF,MAAMkS,UAAU,GAAGA,CAAA;IAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;IAAA,OAAO;MACxB7G,UAAU,EAAG,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAAmV,mBAAA,GAAhBnV,gBAAgB,CAAEkD,EAAE,cAAAiS,mBAAA,uBAApBA,mBAAA,CAAsBtS,SAAS,KAAI,WAAW;MAC3D6L,UAAU,EAAG2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAAoV,oBAAA,GAAhBpV,gBAAgB,CAAEkD,EAAE,cAAAkS,oBAAA,uBAApBA,oBAAA,CAAsBtS,QAAQ,KAAI,MAAM,CAAC;MACpEC,QAAQ,EAAG,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAAqV,oBAAA,GAAhBrV,gBAAgB,CAAEkD,EAAE,cAAAmS,oBAAA,uBAApBA,oBAAA,CAAsBtS,QAAQ,KAAI,EAAE,IAAI;MACtDC,KAAK,EAAG,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAAsV,oBAAA,GAAhBtV,gBAAgB,CAAEkD,EAAE,cAAAoS,oBAAA,uBAApBA,oBAAA,CAAsBtS,KAAK,KAAI;IACzC,CAAC;EAAA,CAAC;EAEF,MAAMuS,UAAU,GAAGA,CAAA;IAAA,IAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;IAAA,OAAO;MACxBlH,UAAU,EAAG,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAAwV,oBAAA,GAAhBxV,gBAAgB,CAAEmD,EAAE,cAAAqS,oBAAA,uBAApBA,oBAAA,CAAsB3S,SAAS,KAAI,WAAW;MAC3D6L,UAAU,EAAG2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAAyV,oBAAA,GAAhBzV,gBAAgB,CAAEmD,EAAE,cAAAsS,oBAAA,uBAApBA,oBAAA,CAAsB3S,QAAQ,KAAI,MAAM,CAAC;MACpEC,QAAQ,EAAG,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAA0V,oBAAA,GAAhB1V,gBAAgB,CAAEmD,EAAE,cAAAuS,oBAAA,uBAApBA,oBAAA,CAAsB3S,QAAQ,KAAI,EAAE,IAAI;MACtDC,KAAK,EAAG,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAA2V,oBAAA,GAAhB3V,gBAAgB,CAAEmD,EAAE,cAAAwS,oBAAA,uBAApBA,oBAAA,CAAsB3S,KAAK,KAAI;IACzC,CAAC;EAAA,CAAC;EAEF,MAAM4S,eAAe,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC7BvH,UAAU,EAAE,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAA6V,qBAAA,GAAhB7V,gBAAgB,CAAEqD,OAAO,cAAAwS,qBAAA,uBAAzBA,qBAAA,CAA2BhT,SAAS,KAAI,WAAW;MAC/D6L,UAAU,EAAE2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAA8V,sBAAA,GAAhB9V,gBAAgB,CAAEqD,OAAO,cAAAyS,sBAAA,uBAAzBA,sBAAA,CAA2BhT,QAAQ,KAAI,SAAS,CAAC;MAC3EC,QAAQ,EAAE,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAA+V,sBAAA,GAAhB/V,gBAAgB,CAAEqD,OAAO,cAAA0S,sBAAA,uBAAzBA,sBAAA,CAA2BhT,QAAQ,KAAI,EAAE,IAAI;MAC1DC,KAAK,EAAE,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAAgW,sBAAA,GAAhBhW,gBAAgB,CAAEqD,OAAO,cAAA2S,sBAAA,uBAAzBA,sBAAA,CAA2BhT,KAAK,KAAI,SAAS;MACpDiT,UAAU,EAAE,KAAK;MACjBtH,MAAM,EAAE;IACV,CAAC;EAAA,CAAC;EAEF,MAAMuH,kBAAkB,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAChC7H,UAAU,EAAE,CAAAzO,gBAAgB,aAAhBA,gBAAgB,wBAAAmW,qBAAA,GAAhBnW,gBAAgB,CAAEoD,UAAU,cAAA+S,qBAAA,uBAA5BA,qBAAA,CAA8BtT,SAAS,KAAI,WAAW;MAClE6L,UAAU,EAAE2F,aAAa,CAAC,CAAArU,gBAAgB,aAAhBA,gBAAgB,wBAAAoW,sBAAA,GAAhBpW,gBAAgB,CAAEoD,UAAU,cAAAgT,sBAAA,uBAA5BA,sBAAA,CAA8BtT,QAAQ,KAAI,MAAM,CAAC;MAC3EC,QAAQ,EAAE,GAAG,CAAA/C,gBAAgB,aAAhBA,gBAAgB,wBAAAqW,sBAAA,GAAhBrW,gBAAgB,CAAEoD,UAAU,cAAAiT,sBAAA,uBAA5BA,sBAAA,CAA8BtT,QAAQ,KAAI,EAAE,IAAI;MAC7DC,KAAK,EAAE,CAAAhD,gBAAgB,aAAhBA,gBAAgB,wBAAAsW,sBAAA,GAAhBtW,gBAAgB,CAAEoD,UAAU,cAAAkT,sBAAA,uBAA5BA,sBAAA,CAA8BtT,KAAK,KAAI,SAAS;MACvDgD,OAAO,EAAE;IACX,CAAC;EAAA,CAAC;;EAEF;EACA,IAAI5F,iBAAiB,IAAIU,wBAAwB,IAAIJ,aAAa,IAAI,CAACV,gBAAgB,EAAE;IACvF,oBACEhB,OAAA;MAAKsS,SAAS,EAAC,2CAA2C;MAAAJ,QAAA,eACxDlS,OAAA;QAAKsS,SAAS,EAAC,aAAa;QAAAJ,QAAA,gBAC1BlS,OAAA,CAAC9C,gBAAgB;UAACqa,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B3X,OAAA;UAAKsS,SAAS,EAAC,4BAA4B;UAAAJ,QAAA,EAAC;QAAmB;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIvU,oBAAoB,IAAIF,mBAAmB,KAAK,IAAI,EAAE;IACxD,oBACElD,OAAA;MAAKsS,SAAS,EAAC,2CAA2C;MAAAJ,QAAA,eACxDlS,OAAA;QAAKsS,SAAS,EAAC,aAAa;QAAAJ,QAAA,gBAC1BlS,OAAA,CAAC9C,gBAAgB;UAACqa,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B3X,OAAA;UAAKsS,SAAS,EAAC,4BAA4B;UAAAJ,QAAA,EAAC;QAAiC;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,WAAW,GAAG1U,mBAAmB,IAAIA,mBAAmB,CAAC0C,gBAAgB,KAAK,WAAW;EAE/F,oBACE5F,OAAA;IAAKsS,SAAS,EAAC,+BAA+B;IAAAJ,QAAA,gBAE5ClS,OAAA,CAAC5B,QAAQ;MACPyZ,IAAI,EAAE3V,WAAY;MAClB4V,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE5D,kBAAmB;MAC5B6D,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhG,QAAA,eAExDlS,OAAA,CAAC3B,KAAK;QACJ0Z,OAAO,EAAE5D,kBAAmB;QAC5BgE,QAAQ,EAAErX,SAAS,IAAIQ,aAAa,IAAIU,oBAAoB,GAAG,OAAO,GAAG,SAAU;QACnFoW,OAAO,EAAC,QAAQ;QAChBC,EAAE,EAAE;UACFC,eAAe,EAAExX,SAAS,IAAIQ,aAAa,IAAIU,oBAAoB,GAAG,SAAS,GAAG,SAAS;UAC3F,kBAAkB,EAAE;YAClBgC,KAAK,EAAE;UACT;QACF,CAAE;QAAAkO,QAAA,EAED9P;MAAc;QAAAoV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX3X,OAAA,CAAC5C,MAAM;MACLya,IAAI,EAAE/U,oBAAqB;MAC3BiV,OAAO,EAAExD,gBAAiB;MAC1B,mBAAgB,mBAAmB;MACnC,oBAAiB,yBAAyB;MAAArC,QAAA,gBAE1ClS,OAAA,CAACxC,WAAW;QAACgD,EAAE,EAAC,mBAAmB;QAAA0R,QAAA,EAAC;MAEpC;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd3X,OAAA,CAAC1C,aAAa;QAAA4U,QAAA,eACZlS,OAAA,CAACzC,iBAAiB;UAACiD,EAAE,EAAC,yBAAyB;UAAA0R,QAAA,EAAC;QAEhD;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB3X,OAAA,CAAC3C,aAAa;QAAA6U,QAAA,gBACZlS,OAAA,CAACvC,MAAM;UAAC8a,OAAO,EAAEhE,gBAAiB;UAACvQ,KAAK,EAAC,SAAS;UAAAkO,QAAA,EAAC;QAEnD;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3X,OAAA,CAACvC,MAAM;UACL8a,OAAO,EAAEjE,iBAAkB;UAC3BtQ,KAAK,EAAC,SAAS;UACfoU,OAAO,EAAC,WAAW;UACnBI,QAAQ,EAAE9V,gBAAiB;UAAAwP,QAAA,EAE1BxP,gBAAgB,gBACf1C,OAAA,CAAAE,SAAA;YAAAgS,QAAA,gBACElS,OAAA,CAAC9C,gBAAgB;cAACqa,IAAI,EAAE,EAAG;cAACc,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE/C;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3X,OAAA,CAAC5C,MAAM;MACLya,IAAI,EAAE7U,qBAAsB;MAC5B+U,OAAO,EAAE5C,iBAAkB;MAC3B,mBAAgB,oBAAoB;MACpC,oBAAiB,0BAA0B;MAAAjD,QAAA,gBAE3ClS,OAAA,CAACxC,WAAW;QAACgD,EAAE,EAAC,oBAAoB;QAAA0R,QAAA,EAAC;MAErC;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd3X,OAAA,CAAC1C,aAAa;QAAA4U,QAAA,eACZlS,OAAA,CAACzC,iBAAiB;UAACiD,EAAE,EAAC,0BAA0B;UAAA0R,QAAA,EAAC;QAEjD;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB3X,OAAA,CAAC3C,aAAa;QAAA6U,QAAA,gBACZlS,OAAA,CAACvC,MAAM;UAAC8a,OAAO,EAAEpD,iBAAkB;UAACnR,KAAK,EAAC,SAAS;UAAAkO,QAAA,EAAC;QAEpD;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3X,OAAA,CAACvC,MAAM;UACL8a,OAAO,EAAE1D,kBAAmB;UAC5B7Q,KAAK,EAAC,SAAS;UACfoU,OAAO,EAAC,WAAW;UACnBI,QAAQ,EAAE5V,mBAAoB,CAAC;UAAA;UAAAsP,QAAA,EAE9BtP,mBAAmB,gBAClB5C,OAAA,CAAAE,SAAA;YAAAgS,QAAA,gBACElS,OAAA,CAAC9C,gBAAgB;cAACqa,IAAI,EAAE,EAAG;cAACc,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE/C;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGRC,WAAW,iBACV5X,OAAA;MAAKsS,SAAS,EAAC,8EAA8E;MAAAJ,QAAA,gBAC3FlS,OAAA;QAAKsS,SAAS,EAAC,6DAA6D;QAAAJ,QAAA,eAC1ElS,OAAA,CAAC/C,OAAO;UAAC0L,KAAK,EAAC,iBAAiB;UAAC+P,SAAS,EAAC,QAAQ;UAAAxG,QAAA,eACjDlS,OAAA,CAAChD,UAAU;YACTub,OAAO,EAAE7E,qBAAsB;YAC/B2E,EAAE,EAAE;cACFrU,KAAK,EAAE,iBAAiB;cACxBgD,OAAO,EAAE,KAAK;cACd2R,WAAW,EAAE,MAAM;cACnB,SAAS,EAAE;gBACTL,eAAe,EAAE;cACnB,CAAC;cACD,SAAS,EAAE;gBACTM,OAAO,EAAE;cACX,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAA3G,QAAA,eAEFlS,OAAA,CAAC/B,SAAS;cAAC8F,QAAQ,EAAC;YAAQ;cAAAyT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEP,CAAC,eAER3X,OAAA;QAAKsS,SAAS,EAAC,6CAA6C;QAAAJ,QAAA,gBAE1DlS,OAAA;UAAIsS,SAAS,EAAC,iDAAiD;UAAAJ,QAAA,EAAC;QAAkB;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEvF3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAM;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzE3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,qKAAgK;cACtK+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAAC4C,MAAM,CAACC,SAAU;gBACzCoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,QAAQ,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBAC5EqE,EAAE,EAAE;kBACF,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAAC4C,MAAM,CAACE,QAAS;kBACxCmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBAC3EqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACZ,MAAM,CAACa,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACZ,MAAM,CAACc,GAAG,EAAC,GAC9E;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACZ,MAAM,CAACa,GAAG;kBACnCC,GAAG,EAAEF,mBAAmB,CAACZ,MAAM,CAACc;gBAClC,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAAC4C,MAAM,CAACG,QAAS;gBACxCkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACrFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAAC4C,MAAM,CAACI,KAAM;gBACrCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACxEqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAO;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,sJAAsJ;cAC5J+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAACiD,OAAO,CAACJ,SAAU;gBAC1CoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBAC5EqE,EAAE,EAAE;kBACH,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAACiD,OAAO,CAACH,QAAS;kBACzCmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBAC5EqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACP,OAAO,CAACQ,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACP,OAAO,CAACS,GAAG,EAAC,GAChF;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACP,OAAO,CAACQ,GAAG;kBACpCC,GAAG,EAAEF,mBAAmB,CAACP,OAAO,CAACS;gBACnC,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAACiD,OAAO,CAACF,QAAS;gBACzCkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACtFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAACiD,OAAO,CAACD,KAAM;gBACtCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACzEqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAE;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,sJAAsJ;cAC5J+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAACkD,EAAE,CAACL,SAAU;gBACrCoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACvEqE,EAAE,EAAE;kBACH,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAACkD,EAAE,CAACJ,QAAS;kBACpCmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBACvEqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACN,EAAE,CAACO,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACN,EAAE,CAACQ,GAAG,EAAC,GACtE;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACN,EAAE,CAACO,GAAG;kBAC/BC,GAAG,EAAEF,mBAAmB,CAACN,EAAE,CAACQ;gBAC9B,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAACkD,EAAE,CAACH,QAAS;gBACpCkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACjFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAACkD,EAAE,CAACF,KAAM;gBACjCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACpEqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAE;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,sJAAsJ;cAC5J+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAACmD,EAAE,CAACN,SAAU;gBACrCoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACvEqE,EAAE,EAAE;kBACH,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAACmD,EAAE,CAACL,QAAS;kBACpCmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBACvEqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACL,EAAE,CAACM,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACL,EAAE,CAACO,GAAG,EAAC,GACtE;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACL,EAAE,CAACM,GAAG;kBAC/BC,GAAG,EAAEF,mBAAmB,CAACL,EAAE,CAACO;gBAC9B,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAACmD,EAAE,CAACJ,QAAS;gBACpCkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACjFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAACmD,EAAE,CAACH,KAAM;gBACjCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACpEqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAW;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,sJAAsJ;cAC5J+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAACoD,UAAU,CAACP,SAAU;gBAC7CoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,YAAY,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBAChFqE,EAAE,EAAE;kBACF,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAACoD,UAAU,CAACN,QAAS;kBAC5CmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBAC/EqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACJ,UAAU,CAACK,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACJ,UAAU,CAACM,GAAG,EAAC,GACtF;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACJ,UAAU,CAACK,GAAG;kBACvCC,GAAG,EAAEF,mBAAmB,CAACJ,UAAU,CAACM;gBACtC,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAACoD,UAAU,CAACL,QAAS;gBAC5CkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACzFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAACoD,UAAU,CAACJ,KAAM;gBACzCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBAC5EqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAIN3X,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YAAKsS,SAAS,EAAC,8BAA8B;YAAAJ,QAAA,gBAC3ClS,OAAA;cAAIsS,SAAS,EAAC,+CAA+C;cAAAJ,QAAA,EAAC;YAAO;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E3X,OAAA,CAAC/C,OAAO;cACN0L,KAAK,EAAC,kLAAkL;cACxL+P,SAAS,EAAC,OAAO;cACjBI,KAAK;cAAA5G,QAAA,eAELlS,OAAA,CAACF,QAAQ;gBACPiE,QAAQ,EAAC,OAAO;gBAChBuO,SAAS,EAAC,mCAAmC;gBAC7C+F,EAAE,EAAE;kBAAEtU,QAAQ,EAAE;gBAAO;cAAE;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBACnBlS,OAAA,CAACpC,UAAU;cACTya,EAAE,EAAE;gBACFtU,QAAQ,EAAE,SAAS;gBACnB2L,UAAU,EAAE,GAAG;gBACf1L,KAAK,EAAE,iBAAiB;gBACxB+U,YAAY,EAAE;cAChB,CAAE;cAAA7G,QAAA,EACH;YAED;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;cAACmb,SAAS;cAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;gBACLsW,KAAK,EAAEhT,gBAAgB,CAACqD,OAAO,CAACR,SAAU;gBAC1CoV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBAC7EqE,EAAE,EAAE;kBACF,qBAAqB,EAAE;oBACrBtU,QAAQ,EAAE,UAAU;oBACpBiD,OAAO,EAAE,UAAU;oBACnB0I,UAAU,EAAE;kBACd,CAAC;kBACD,oCAAoC,EAAE;oBACpC0J,WAAW,EAAE;kBACf,CAAC;kBACD,0CAA0C,EAAE;oBAC1CA,WAAW,EAAE;kBACf,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAAlH,QAAA,EAED5N,UAAU,CAACuH,GAAG,CAACwN,IAAI,iBAClBrZ,OAAA,CAACrC,QAAQ;kBAAYqW,KAAK,EAAEqF,IAAK;kBAAAnH,QAAA,EAAEmH;gBAAI,GAAxBA,IAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN3X,OAAA;YAAKsS,SAAS,EAAC,6BAA6B;YAAAJ,QAAA,gBAC1ClS,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAACnC,WAAW;gBAACmb,SAAS;gBAAA9G,QAAA,eACpBlS,OAAA,CAACtC,MAAM;kBACLsW,KAAK,EAAEhT,gBAAgB,CAACqD,OAAO,CAACP,QAAS;kBACzCmV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;kBAC5EqE,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrBtU,QAAQ,EAAE,UAAU;sBACpBiD,OAAO,EAAE,WAAW;sBACpB0I,UAAU,EAAE;oBACd,CAAC;oBACD,oCAAoC,EAAE;sBACpC0J,WAAW,EAAE;oBACf,CAAC;oBACD,0CAA0C,EAAE;sBAC1CA,WAAW,EAAE;oBACf,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,WAAW,EAAE;oBACf;kBACF,CAAE;kBAAAlH,QAAA,EAED3N,SAAS,CAACsH,GAAG,CAAC1B,IAAI,iBACjBnK,OAAA,CAACrC,QAAQ;oBAAYqW,KAAK,EAAE7J,IAAK;oBAAA+H,QAAA,EAAE/H;kBAAI,GAAxBA,IAAI;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA+B,CACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,GACH,aACY,EAAC1N,mBAAmB,CAACH,OAAO,CAACI,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACH,OAAO,CAACK,GAAG,EAAC,GAChF;cAAA;gBAAA8S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,QAAQ;gBACbmP,UAAU,EAAE;kBACV7U,GAAG,EAAED,mBAAmB,CAACH,OAAO,CAACI,GAAG;kBACpCC,GAAG,EAAEF,mBAAmB,CAACH,OAAO,CAACK;gBACnC,CAAE;gBACFsP,KAAK,EAAEhT,gBAAgB,CAACqD,OAAO,CAACN,QAAS;gBACzCkV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAE0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAC,CAAE;gBACtFqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BtU,QAAQ,EAAE,UAAU;oBACpB,YAAY,EAAE;sBACZqV,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAwQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3X,OAAA;cAAAkS,QAAA,gBACElS,OAAA,CAACpC,UAAU;gBACTya,EAAE,EAAE;kBACFtU,QAAQ,EAAE,SAAS;kBACnB2L,UAAU,EAAE,GAAG;kBACf1L,KAAK,EAAE,iBAAiB;kBACxB+U,YAAY,EAAE;gBAChB,CAAE;gBAAA7G,QAAA,EACH;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3X,OAAA,CAAClC,SAAS;gBAACkb,SAAS;gBAClB7O,IAAI,EAAC,OAAO;gBACZ6J,KAAK,EAAEhT,gBAAgB,CAACqD,OAAO,CAACL,KAAM;gBACtCiV,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;gBACzEqE,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1BxQ,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE;sBACZuR,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBA,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF,CAAC;kBACD,2BAA2B,EAAE;oBAC3BpS,OAAO,EAAE,GAAG;oBACZa,MAAM,EAAE,MAAM;oBACd2R,MAAM,EAAE;kBACV;gBACF;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3X,OAAA;UAAKsS,SAAS,EAAC,+BAA+B;UAAAJ,QAAA,eAC5ClS,OAAA;YAAKsS,SAAS,EAAC,qBAAqB;YAAAJ,QAAA,gBAClClS,OAAA;cACEuY,OAAO,EAAE/D,oBAAqB;cAC9BgE,QAAQ,EAAE5V,mBAAoB,CAAC;cAAA;cAC/B0P,SAAS,EAAC,gRAAgR;cAAAJ,QAAA,EAEzRtP,mBAAmB,gBAClB5C,OAAA,CAAAE,SAAA;gBAAAgS,QAAA,gBACElS,OAAA,CAAC9C,gBAAgB;kBAACqa,IAAI,EAAE,EAAG;kBAACc,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAET3X,OAAA;cACEuY,OAAO,EAAElE,UAAW;cACpBmE,QAAQ,EAAE9V,gBAAiB,CAAC;cAAA;cAC5B4P,SAAS,EAAC,gRAAgR;cAAAJ,QAAA,EAEzRxP,gBAAgB,gBACf1C,OAAA,CAAAE,SAAA;gBAAAgS,QAAA,gBACElS,OAAA,CAAC9C,gBAAgB;kBAACqa,IAAI,EAAE,EAAG;kBAACc,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE/C;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACJ,eAGD3X,OAAA;MAAKsS,SAAS,EAAC,+BAA+B;MAACjF,KAAK,EAAE;QAAEoM,UAAU,EAAE7B,WAAW,GAAG,OAAO,GAAG;MAAI,CAAE;MAAA1F,QAAA,GAE/F0F,WAAW,iBACV5X,OAAA;QAAKsS,SAAS,EAAC,2EAA2E;QACxFjF,KAAK,EAAE;UAAEa,IAAI,EAAE0J,WAAW,GAAG,OAAO,GAAG,GAAG;UAAEzJ,KAAK,EAAE;QAAI,CAAE;QAAA+D,QAAA,eAEzDlS,OAAA;UAAKsS,SAAS,EAAC,yCAAyC;UAAAJ,QAAA,eACtDlS,OAAA,CAAC7C,GAAG;YAACkb,EAAE,EAAE;cAAEhK,OAAO,EAAE,MAAM;cAAEqL,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAzH,QAAA,gBACzDlS,OAAA,CAAC/C,OAAO;cAAC0L,KAAK,EAAE/H,aAAa,GAAG,iBAAiB,GAAG,aAAc;cAAC8X,SAAS,EAAC,KAAK;cAAAxG,QAAA,eAChFlS,OAAA;gBAAAkS,QAAA,eACElS,OAAA,CAAChD,UAAU;kBACTub,OAAO,EAAEnD,YAAa;kBACtBoD,QAAQ,EAAE5X,aAAc;kBACxByX,EAAE,EAAE;oBACFrU,KAAK,EAAEpD,aAAa,GAAG,0BAA0B,GAAG,iBAAiB;oBACrE,SAAS,EAAE;sBACT0X,eAAe,EAAE;oBACnB,CAAC;oBACD,SAAS,EAAE;sBACTM,OAAO,EAAE;oBACX,CAAC;oBACD,YAAY,EAAE;sBACZ5U,KAAK,EAAE,0BAA0B;sBACjCwV,MAAM,EAAE;oBACV,CAAC;oBACDX,UAAU,EAAE,UAAU;oBACtB7R,OAAO,EAAE;kBACX,CAAE;kBAAAkL,QAAA,EAEDtR,aAAa,gBACZZ,OAAA,CAAC9C,gBAAgB;oBAACqa,IAAI,EAAE,EAAG;oBAACvT,KAAK,EAAC;kBAAS;oBAAAwT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9C3X,OAAA,CAAChC,QAAQ;oBAAC+F,QAAQ,EAAC;kBAAQ;oBAAAyT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC9B;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEV3X,OAAA,CAAC/C,OAAO;cAAC0L,KAAK,EAAEnG,eAAe,GAAG,mBAAmB,GAAG,4CAA6C;cAACkW,SAAS,EAAC,KAAK;cAAAxG,QAAA,eACnHlS,OAAA;gBAAAkS,QAAA,eACElS,OAAA,CAAChD,UAAU;kBACTub,OAAO,EAAEjM,iBAAkB;kBAC3BkM,QAAQ,EAAEhW,eAAe,IAAI5B,aAAc;kBAC3CyX,EAAE,EAAE;oBACFrU,KAAK,EAAGxB,eAAe,IAAI5B,aAAa,GAAI,0BAA0B,GAAG,iBAAiB;oBAC1F,SAAS,EAAE;sBACT0X,eAAe,EAAE;oBACnB,CAAC;oBACD,SAAS,EAAE;sBACTM,OAAO,EAAE;oBACX,CAAC;oBACD,YAAY,EAAE;sBACZ5U,KAAK,EAAE,0BAA0B;sBACjCwV,MAAM,EAAE;oBACV,CAAC;oBACDX,UAAU,EAAE,UAAU;oBACtB7R,OAAO,EAAE;kBACX,CAAE;kBAAAkL,QAAA,EAED1P,eAAe,gBACdxC,OAAA,CAAC9C,gBAAgB;oBAACqa,IAAI,EAAE,EAAG;oBAACvT,KAAK,EAAC;kBAAS;oBAAAwT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9C3X,OAAA,CAAC7B,YAAY;oBAAC4F,QAAQ,EAAC;kBAAQ;oBAAAyT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD3X,OAAA;QACEsS,SAAS,EAAC,oCAAoC;QAC9CjF,KAAK,EAAE;UAAEuM,SAAS,EAAEhC,WAAW,GAAG,MAAM,GAAG,KAAK;UAAEhQ,KAAK,EAAE;QAAO,CAAE;QAAAsK,QAAA,GAGjE,CAAC0F,WAAW,iBACX5X,OAAA;UAAKsS,SAAS,EAAC,wBAAwB;UAAAJ,QAAA,eACrClS,OAAA,CAAC/C,OAAO;YAAC0L,KAAK,EAAC,iBAAiB;YAAC+P,SAAS,EAAC,QAAQ;YAAAxG,QAAA,eACjDlS,OAAA,CAAChD,UAAU;cACTub,OAAO,EAAE7E,qBAAsB;cAC/B2E,EAAE,EAAE;gBACFrU,KAAK,EAAE,iBAAiB;gBACxBgD,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE;kBACTsR,eAAe,EAAE;gBACnB,CAAC;gBACD,SAAS,EAAE;kBACTM,OAAO,EAAE;gBACX,CAAC;gBACDC,UAAU,EAAE;cACd,CAAE;cAAA3G,QAAA,eAEFlS,OAAA,CAAC/B,SAAS;gBAAC8F,QAAQ,EAAC;cAAQ;gBAAAyT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAEA/W,aAAa,gBACtBZ,OAAA;UAAKsS,SAAS,EAAC,uCAAuC;UAAAJ,QAAA,eACpDlS,OAAA;YAAKsS,SAAS,EAAC,aAAa;YAAAJ,QAAA,gBAC1BlS,OAAA,CAAC9C,gBAAgB;cAACqa,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B3X,OAAA;cAAKsS,SAAS,EAAC,4BAA4B;cAAAJ,QAAA,EAAC;YAAsB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ7W,SAAS,gBACXd,OAAA;UAAKsS,SAAS,EAAC,uCAAuC;UAAAJ,QAAA,eACpDlS,OAAA;YAAKsS,SAAS,EAAC,aAAa;YAAAJ,QAAA,EACzB9O,oBAAoB,gBACnBpD,OAAA,CAAAE,SAAA;cAAAgS,QAAA,gBACElS,OAAA,CAAC9C,gBAAgB;gBAACqa,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B3X,OAAA;gBAAKsS,SAAS,EAAC,4BAA4B;gBAAAJ,QAAA,EAAC;cAA6B;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eAC/E,CAAC,GACDzU,mBAAmB,IAAIA,mBAAmB,CAAC0C,gBAAgB,KAAK,WAAW,gBAC7E5F,OAAA,CAAAE,SAAA;cAAAgS,QAAA,gBACElS,OAAA;gBAAKsS,SAAS,EAAC,2BAA2B;gBAAAJ,QAAA,EAAC;cAAoC;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrF3X,OAAA;gBAAKsS,SAAS,EAAC,oBAAoB;gBAAAJ,QAAA,EAAC;cAEpC;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGLrU,iBAAiB,KAAK,IAAI,iBACzBtD,OAAA;gBAAKsS,SAAS,EAAC,4BAA4B;gBAAAJ,QAAA,GAAC,gDACI,eAAAlS,OAAA;kBAAMsS,SAAS,EAAC,6BAA6B;kBAAAJ,QAAA,EAAE5O;gBAAiB;kBAAAkU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,YACxH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,eAED3X,OAAA;gBACEuY,OAAO,EAAE9D,0BAA2B;gBACpCnC,SAAS,EAAC,mFAAmF;gBAAAJ,QAAA,EAC9F;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC,gBAEH3X,OAAA,CAAAE,SAAA;cAAAgS,QAAA,gBACElS,OAAA;gBAAKsS,SAAS,EAAC,2BAA2B;gBAAAJ,QAAA,EAAC;cAA0B;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3E3X,OAAA;gBACEuY,OAAO,EAAE1R,eAAgB;gBACzByL,SAAS,EAAC,mFAAmF;gBAAAJ,QAAA,EAC9F;cAED;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJC,WAAW,gBACb5X,OAAA,CAAAE,SAAA;UAAAgS,QAAA,gBAGElS,OAAA,CAACL,kBAAkB;YACjBe,UAAU,EAAEA;UAAW;YAAA8W,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEF3X,OAAA,CAACzB,eAAe;YACdsb,gBAAgB,EAAEhE,eAAe,CAAC,CAAE;YACpCiE,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCwE,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpCoD,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1CxW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACxB,aAAa;YACZsb,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCsE,gBAAgB,EAAEhE,eAAe,CAAC,CAAE;YACpCmE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpCqD,WAAW,EAAE/D,UAAU,CAAC,CAAE;YAC1BgE,WAAW,EAAE3D,UAAU,CAAC,CAAE;YAC1B7V,UAAU,EAAEA;UAAW;YAAA8W,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACF3X,OAAA,CAACvB,mBAAmB;YAClBqb,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpCuD,UAAU,EAAEzZ,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACtB,uBAAuB;YACtBob,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpClW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACrB,8BAA8B;YAC7Bmb,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpCwD,eAAe,EAAE1Z,UAAW;YAC5BkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACpB,yBAAyB;YACxBkb,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpCyD,aAAa,EAAE3Z,UAAW;YAC1BkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACnB,0BAA0B;YACzBib,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpClW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAAClB,0BAA0B;YACzBgb,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpClW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAACjB,sBAAsB;YACrB+a,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpClW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3X,OAAA,CAAChB,qBAAqB;YACpB8a,eAAe,EAAEvE,cAAc,CAAC,CAAE;YAClCyE,mBAAmB,EAAE9C,kBAAkB,CAAC,CAAE;YAC1C6C,gBAAgB,EAAEnD,eAAe,CAAC,CAAE;YACpClW,UAAU,EAAEA,UAAW;YACvBkB,eAAe,EAAEA;UAAgB;YAAA4V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA,eACF,CAAC,GACS,IAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvX,EAAA,CA7yFID,4BAA4B;EAAA,QACT7B,eAAe,EAEvBY,SAAS,EA4CPD,WAAW;AAAA;AAAAqb,EAAA,GA/CxBna,4BAA4B;AA+yFlC,eAAeA,4BAA4B;AAAC,IAAAma,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}